<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الوحدة 1: مدخل إلى عالم الهندسة الإكلينيكية</title>
    <meta name="description" content="تعريف الهندسة الإكلينيكية ونشأتها التاريخية، أدوار ومسؤوليات المهندس الإكلينيكي">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../../css/main.css">
    <link rel="stylesheet" href="../../css/components.css">
    <link rel="stylesheet" href="../../css/animations.css">
    <link rel="stylesheet" href="../../css/responsive.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="../../assets/icons/logo.svg" alt="شعار المنهج" class="logo-img">
                <span class="logo-text">منهج الهندسة الإكلينيكية</span>
            </div>
            <div class="nav-menu">
                <a href="../../index.html" class="nav-link">الرئيسية</a>
                <a href="../about.html" class="nav-link">حول المنهج</a>
                <a href="../curriculum.html" class="nav-link">المحتوى</a>
                <a href="../units.html" class="nav-link">الوحدات</a>
                <a href="../resources.html" class="nav-link">المصادر</a>
                <a href="../contact.html" class="nav-link">التواصل</a>
            </div>
        </div>
    </nav>

    <!-- Unit Header -->
    <section class="unit-header">
        <div class="container">
            <div class="unit-header-content">
                <div class="unit-badge">الوحدة 1</div>
                <h1 class="unit-title">مدخل إلى عالم الهندسة الإكلينيكية</h1>
                <p class="unit-description">تعريف الهندسة الإكلينيكية ونشأتها التاريخية، أدوار ومسؤوليات المهندس الإكلينيكي</p>
                <div class="unit-meta">
                    <span><i class="fas fa-book"></i> 3 فصول</span>
                    <span><i class="fas fa-clock"></i> 8 ساعات</span>
                    <span><i class="fas fa-users"></i> مستوى مبتدئ</span>
                </div>
                <nav class="breadcrumb">
                    <a href="../../index.html">الرئيسية</a>
                    <span class="separator">/</span>
                    <a href="../units.html">الوحدات</a>
                    <span class="separator">/</span>
                    <span class="current">الوحدة 1</span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Unit Progress -->
    <section class="unit-progress-section">
        <div class="container">
            <div class="progress-card">
                <div class="progress-header">
                    <h3>تقدمك في هذه الوحدة</h3>
                    <span class="progress-percentage">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
                <div class="progress-details">
                    <span>0 من 3 فصول مكتملة</span>
                    <span>0 من 8 ساعات مدروسة</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Unit Content -->
    <section class="unit-content">
        <div class="container">
            <div class="content-layout">
                <!-- Sidebar Navigation -->
                <aside class="content-sidebar">
                    <div class="sidebar-content">
                        <h4>محتويات الوحدة</h4>
                        <nav class="chapter-nav">
                            <a href="#chapter1" class="chapter-link active">
                                <span class="chapter-number">1</span>
                                <span class="chapter-title">تعريف الهندسة الإكلينيكية</span>
                                <span class="chapter-status"><i class="fas fa-circle"></i></span>
                            </a>
                            <a href="#chapter2" class="chapter-link">
                                <span class="chapter-number">2</span>
                                <span class="chapter-title">التطور التاريخي</span>
                                <span class="chapter-status"><i class="fas fa-circle"></i></span>
                            </a>
                            <a href="#chapter3" class="chapter-link">
                                <span class="chapter-number">3</span>
                                <span class="chapter-title">أدوار ومسؤوليات المهندس الإكلينيكي</span>
                                <span class="chapter-status"><i class="fas fa-circle"></i></span>
                            </a>
                        </nav>
                        
                        <div class="sidebar-actions">
                            <button class="btn btn-primary btn-block">
                                <i class="fas fa-play"></i>
                                ابدأ الدراسة
                            </button>
                            <button class="btn btn-secondary btn-block">
                                <i class="fas fa-download"></i>
                                تحميل المحتوى
                            </button>
                        </div>
                    </div>
                </aside>

                <!-- Main Content -->
                <main class="main-content">
                    <!-- Chapter 1 -->
                    <article id="chapter1" class="chapter">
                        <header class="chapter-header">
                            <div class="chapter-badge">الفصل 1</div>
                            <h2>تعريف الهندسة الإكلينيكية</h2>
                            <div class="chapter-meta">
                                <span><i class="fas fa-clock"></i> 2.5 ساعة</span>
                                <span><i class="fas fa-bookmark"></i> أساسي</span>
                            </div>
                        </header>

                        <div class="chapter-content">
                            <div class="learning-objectives">
                                <h3>أهداف التعلم</h3>
                                <ul>
                                    <li>فهم تعريف الهندسة الإكلينيكية وأهميتها</li>
                                    <li>التعرف على العلاقة بين الهندسة والطب</li>
                                    <li>إدراك دور التكنولوجيا في الرعاية الصحية</li>
                                    <li>فهم المفاهيم الأساسية للمجال</li>
                                </ul>
                            </div>

                            <div class="content-section">
                                <h3>ما هي الهندسة الإكلينيكية؟</h3>
                                <p>الهندسة الإكلينيكية (Clinical Engineering) هي تخصص هندسي متعدد التخصصات يجمع بين المبادئ الهندسية والعلوم الطبية لتطوير وإدارة وصيانة التكنولوجيا الطبية المستخدمة في الرعاية الصحية.</p>
                                
                                <div class="definition-box">
                                    <h4>التعريف الرسمي</h4>
                                    <p>"الهندسة الإكلينيكية هي تطبيق المبادئ والممارسات الهندسية على الرعاية الصحية، وتشمل إدارة التكنولوجيا الطبية وضمان سلامة المرضى والموظفين."</p>
                                    <cite>- الجمعية الأمريكية للهندسة الإكلينيكية (ACCE)</cite>
                                </div>

                                <h4>المجالات الرئيسية</h4>
                                <div class="areas-grid">
                                    <div class="area-card">
                                        <i class="fas fa-tools"></i>
                                        <h5>إدارة التكنولوجيا</h5>
                                        <p>تخطيط وشراء وتشغيل وصيانة الأجهزة الطبية</p>
                                    </div>
                                    <div class="area-card">
                                        <i class="fas fa-shield-alt"></i>
                                        <h5>سلامة المرضى</h5>
                                        <p>ضمان الاستخدام الآمن للتكنولوجيا الطبية</p>
                                    </div>
                                    <div class="area-card">
                                        <i class="fas fa-chart-line"></i>
                                        <h5>تحسين الأداء</h5>
                                        <p>تحليل وتحسين كفاءة الأنظمة الطبية</p>
                                    </div>
                                    <div class="area-card">
                                        <i class="fas fa-graduation-cap"></i>
                                        <h5>التدريب والتعليم</h5>
                                        <p>تدريب الكوادر الطبية على استخدام التكنولوجيا</p>
                                    </div>
                                </div>
                            </div>

                            <div class="content-section">
                                <h3>أهمية الهندسة الإكلينيكية</h3>
                                <p>في عصر التطور التكنولوجي السريع، تلعب الهندسة الإكلينيكية دوراً محورياً في:</p>
                                
                                <div class="importance-list">
                                    <div class="importance-item">
                                        <div class="importance-icon">
                                            <i class="fas fa-heartbeat"></i>
                                        </div>
                                        <div class="importance-content">
                                            <h4>تحسين جودة الرعاية</h4>
                                            <p>ضمان عمل الأجهزة الطبية بأعلى كفاءة لتقديم رعاية صحية متميزة</p>
                                        </div>
                                    </div>
                                    <div class="importance-item">
                                        <div class="importance-icon">
                                            <i class="fas fa-dollar-sign"></i>
                                        </div>
                                        <div class="importance-content">
                                            <h4>خفض التكاليف</h4>
                                            <p>تحسين إدارة الموارد وتقليل تكاليف الصيانة والتشغيل</p>
                                        </div>
                                    </div>
                                    <div class="importance-item">
                                        <div class="importance-icon">
                                            <i class="fas fa-user-shield"></i>
                                        </div>
                                        <div class="importance-content">
                                            <h4>ضمان السلامة</h4>
                                            <p>حماية المرضى والموظفين من مخاطر التكنولوجيا الطبية</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="interactive-element">
                                <h3>اختبر فهمك</h3>
                                <div class="quiz-question">
                                    <p><strong>سؤال:</strong> ما هو الهدف الرئيسي للهندسة الإكلينيكية؟</p>
                                    <div class="quiz-options">
                                        <label><input type="radio" name="q1" value="a"> تصنيع الأجهزة الطبية</label>
                                        <label><input type="radio" name="q1" value="b"> إدارة التكنولوجيا الطبية وضمان سلامة المرضى</label>
                                        <label><input type="radio" name="q1" value="c"> تدريس الطب</label>
                                        <label><input type="radio" name="q1" value="d"> بيع الأجهزة الطبية</label>
                                    </div>
                                    <button class="btn btn-primary quiz-submit">تحقق من الإجابة</button>
                                    <div class="quiz-feedback" style="display: none;">
                                        <div class="feedback-content"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="interactive-element">
                                <h3>محاكي بيئة العمل</h3>
                                <div class="work-simulator">
                                    <h4>يوم في حياة مهندس إكلينيكي</h4>
                                    <p>اختبر معرفتك بالمهام اليومية للمهندس الإكلينيكي:</p>
                                    <div class="scenario-selector">
                                        <button class="scenario-btn" data-scenario="morning">الصباح (8:00 ص)</button>
                                        <button class="scenario-btn" data-scenario="afternoon">بعد الظهر (2:00 م)</button>
                                        <button class="scenario-btn" data-scenario="emergency">حالة طوارئ</button>
                                    </div>
                                    <div class="scenario-content">
                                        <div id="morning-scenario" class="scenario" style="display: none;">
                                            <h5>مهام الصباح</h5>
                                            <p><strong>الموقف:</strong> وصلت إلى المستشفى في بداية نوبة العمل. ما هي أولوياتك؟</p>
                                            <div class="task-options">
                                                <label><input type="checkbox" name="morning-tasks" value="check-reports"> مراجعة تقارير الليلة السابقة</label>
                                                <label><input type="checkbox" name="morning-tasks" value="inspect-icu"> فحص أجهزة العناية المركزة</label>
                                                <label><input type="checkbox" name="morning-tasks" value="team-meeting"> حضور اجتماع الفريق الطبي</label>
                                                <label><input type="checkbox" name="morning-tasks" value="maintenance-schedule"> مراجعة جدول الصيانة اليومي</label>
                                            </div>
                                            <button class="btn btn-secondary check-scenario">تحقق من الإجابة</button>
                                        </div>
                                        <div id="afternoon-scenario" class="scenario" style="display: none;">
                                            <h5>مهام بعد الظهر</h5>
                                            <p><strong>الموقف:</strong> تلقيت بلاغ عن عطل في جهاز غسيل الكلى. كيف تتعامل مع الموقف؟</p>
                                            <div class="task-sequence">
                                                <div class="sequence-step">
                                                    <span class="step-number">1</span>
                                                    <select name="step1">
                                                        <option value="">اختر الخطوة الأولى</option>
                                                        <option value="safety">التأكد من سلامة المريض</option>
                                                        <option value="restart">إعادة تشغيل الجهاز</option>
                                                        <option value="call-vendor">الاتصال بالشركة المصنعة</option>
                                                    </select>
                                                </div>
                                                <div class="sequence-step">
                                                    <span class="step-number">2</span>
                                                    <select name="step2">
                                                        <option value="">اختر الخطوة الثانية</option>
                                                        <option value="document">توثيق العطل</option>
                                                        <option value="backup">البحث عن جهاز بديل</option>
                                                        <option value="diagnose">تشخيص المشكلة</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <button class="btn btn-secondary check-scenario">تحقق من التسلسل</button>
                                        </div>
                                        <div id="emergency-scenario" class="scenario" style="display: none;">
                                            <h5>حالة طوارئ</h5>
                                            <p><strong>الموقف:</strong> انقطاع الكهرباء في المستشفى! ما هي إجراءاتك الفورية؟</p>
                                            <div class="emergency-checklist">
                                                <div class="checklist-item">
                                                    <input type="checkbox" id="generator" name="emergency" value="generator">
                                                    <label for="generator">التأكد من تشغيل المولد الاحتياطي</label>
                                                </div>
                                                <div class="checklist-item">
                                                    <input type="checkbox" id="critical-devices" name="emergency" value="critical">
                                                    <label for="critical-devices">فحص الأجهزة الحيوية (أجهزة التنفس، المراقبة)</label>
                                                </div>
                                                <div class="checklist-item">
                                                    <input type="checkbox" id="communication" name="emergency" value="communication">
                                                    <label for="communication">التواصل مع فريق الطوارئ</label>
                                                </div>
                                                <div class="checklist-item">
                                                    <input type="checkbox" id="backup-power" name="emergency" value="backup">
                                                    <label for="backup-power">التحقق من البطاريات الاحتياطية</label>
                                                </div>
                                            </div>
                                            <button class="btn btn-danger check-scenario">تقييم الاستجابة</button>
                                        </div>
                                    </div>
                                    <div class="scenario-feedback" style="display: none;">
                                        <div class="feedback-content"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>

                    <!-- Chapter 2 -->
                    <article id="chapter2" class="chapter">
                        <header class="chapter-header">
                            <div class="chapter-badge">الفصل 2</div>
                            <h2>التطور التاريخي للهندسة الإكلينيكية</h2>
                            <div class="chapter-meta">
                                <span><i class="fas fa-clock"></i> 3 ساعات</span>
                                <span><i class="fas fa-bookmark"></i> أساسي</span>
                            </div>
                        </header>

                        <div class="chapter-content">
                            <div class="learning-objectives">
                                <h3>أهداف التعلم</h3>
                                <ul>
                                    <li>تتبع التطور التاريخي للهندسة الإكلينيكية</li>
                                    <li>فهم العوامل التي أدت لظهور هذا التخصص</li>
                                    <li>التعرف على المحطات المهمة في تطور المجال</li>
                                    <li>إدراك التحديات التاريخية وكيفية التغلب عليها</li>
                                </ul>
                            </div>

                            <div class="content-section">
                                <h3>البدايات المبكرة (1940-1960)</h3>
                                <p>بدأت الهندسة الإكلينيكية كاستجابة طبيعية للتطور السريع في التكنولوجيا الطبية خلال الحرب العالمية الثانية وما بعدها.</p>

                                <div class="timeline">
                                    <div class="timeline-item">
                                        <div class="timeline-date">1940s</div>
                                        <div class="timeline-content">
                                            <h4>ظهور الأجهزة الطبية المعقدة</h4>
                                            <p>تطوير أجهزة مثل أجهزة التنفس الصناعي وأجهزة مراقبة القلب</p>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-date">1950s</div>
                                        <div class="timeline-content">
                                            <h4>أول المهندسين في المستشفيات</h4>
                                            <p>بدء توظيف مهندسين للتعامل مع الأجهزة الطبية المعقدة</p>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-date">1960s</div>
                                        <div class="timeline-content">
                                            <h4>تأسيس أول أقسام الهندسة الإكلينيكية</h4>
                                            <p>إنشاء أقسام متخصصة في المستشفيات الكبرى</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="content-section">
                                <h3>النضج والتطوير (1970-1990)</h3>
                                <p>شهدت هذه الفترة نضج المجال وتطوير المعايير والممارسات المهنية.</p>

                                <div class="milestone-grid">
                                    <div class="milestone-card">
                                        <div class="milestone-year">1971</div>
                                        <h4>تأسيس ACCE</h4>
                                        <p>تأسيس الجمعية الأمريكية للهندسة الإكلينيكية</p>
                                    </div>
                                    <div class="milestone-card">
                                        <div class="milestone-year">1975</div>
                                        <h4>أول برامج التعليم</h4>
                                        <p>إطلاق أول برامج تعليمية متخصصة في الجامعات</p>
                                    </div>
                                    <div class="milestone-card">
                                        <div class="milestone-year">1980s</div>
                                        <h4>تطوير المعايير</h4>
                                        <p>وضع معايير السلامة والجودة للأجهزة الطبية</p>
                                    </div>
                                    <div class="milestone-card">
                                        <div class="milestone-year">1990s</div>
                                        <h4>الانتشار العالمي</h4>
                                        <p>انتشار التخصص في جميع أنحاء العالم</p>
                                    </div>
                                </div>
                            </div>

                            <div class="content-section">
                                <h3>العصر الحديث (2000-الآن)</h3>
                                <p>يتميز العصر الحالي بالتطور السريع في التكنولوجيا الرقمية والذكاء الاصطناعي.</p>

                                <div class="modern-trends">
                                    <div class="trend-item">
                                        <i class="fas fa-wifi"></i>
                                        <h4>إنترنت الأشياء الطبية</h4>
                                        <p>ربط الأجهزة الطبية بالشبكات لمراقبة أفضل</p>
                                    </div>
                                    <div class="trend-item">
                                        <i class="fas fa-robot"></i>
                                        <h4>الذكاء الاصطناعي</h4>
                                        <p>استخدام AI في التشخيص والعلاج</p>
                                    </div>
                                    <div class="trend-item">
                                        <i class="fas fa-mobile-alt"></i>
                                        <h4>الصحة المحمولة</h4>
                                        <p>أجهزة مراقبة صحية محمولة وقابلة للارتداء</p>
                                    </div>
                                    <div class="trend-item">
                                        <i class="fas fa-cloud"></i>
                                        <h4>الحوسبة السحابية</h4>
                                        <p>تخزين ومعالجة البيانات الطبية في السحابة</p>
                                    </div>
                                </div>
                            </div>

                            <div class="interactive-element">
                                <h3>خط زمني تفاعلي</h3>
                                <div class="interactive-timeline">
                                    <p>انقر على النقاط لاستكشاف المحطات المهمة في تاريخ الهندسة الإكلينيكية</p>
                                    <div class="timeline-interactive">
                                        <div class="timeline-point" data-year="1940" data-event="بداية التكنولوجيا الطبية المعقدة"></div>
                                        <div class="timeline-point" data-year="1971" data-event="تأسيس ACCE"></div>
                                        <div class="timeline-point" data-year="1990" data-event="الانتشار العالمي"></div>
                                        <div class="timeline-point" data-year="2020" data-event="عصر الذكاء الاصطناعي"></div>
                                    </div>
                                    <div class="timeline-details" id="timeline-details">
                                        <p>اختر نقطة زمنية لعرض التفاصيل</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>

                    <!-- Chapter 3 -->
                    <article id="chapter3" class="chapter">
                        <header class="chapter-header">
                            <div class="chapter-badge">الفصل 3</div>
                            <h2>أدوار ومسؤوليات المهندس الإكلينيكي</h2>
                            <div class="chapter-meta">
                                <span><i class="fas fa-clock"></i> 2.5 ساعة</span>
                                <span><i class="fas fa-bookmark"></i> أساسي</span>
                            </div>
                        </header>

                        <div class="chapter-content">
                            <div class="learning-objectives">
                                <h3>أهداف التعلم</h3>
                                <ul>
                                    <li>تحديد الأدوار الرئيسية للمهندس الإكلينيكي</li>
                                    <li>فهم المسؤوليات اليومية والاستراتيجية</li>
                                    <li>التعرف على المهارات المطلوبة</li>
                                    <li>إدراك التحديات المهنية وكيفية مواجهتها</li>
                                </ul>
                            </div>

                            <div class="content-section">
                                <h3>الأدوار الرئيسية</h3>
                                <p>يؤدي المهندس الإكلينيكي مجموعة متنوعة من الأدوار المهمة في النظام الصحي:</p>

                                <div class="roles-grid">
                                    <div class="role-card">
                                        <div class="role-icon">
                                            <i class="fas fa-tools"></i>
                                        </div>
                                        <h4>مدير التكنولوجيا</h4>
                                        <p>إدارة وصيانة الأجهزة الطبية وضمان عملها بكفاءة عالية</p>
                                        <ul>
                                            <li>تخطيط وشراء الأجهزة</li>
                                            <li>إدارة برامج الصيانة</li>
                                            <li>مراقبة الأداء</li>
                                        </ul>
                                    </div>
                                    <div class="role-card">
                                        <div class="role-icon">
                                            <i class="fas fa-shield-alt"></i>
                                        </div>
                                        <h4>ضامن السلامة</h4>
                                        <p>ضمان سلامة المرضى والموظفين من مخاطر التكنولوجيا الطبية</p>
                                        <ul>
                                            <li>تقييم المخاطر</li>
                                            <li>وضع بروتوكولات السلامة</li>
                                            <li>التحقيق في الحوادث</li>
                                        </ul>
                                    </div>
                                    <div class="role-card">
                                        <div class="role-icon">
                                            <i class="fas fa-graduation-cap"></i>
                                        </div>
                                        <h4>مدرب ومعلم</h4>
                                        <p>تدريب الكوادر الطبية على الاستخدام الآمن والفعال للتكنولوجيا</p>
                                        <ul>
                                            <li>تطوير برامج التدريب</li>
                                            <li>إجراء ورش العمل</li>
                                            <li>إعداد المواد التعليمية</li>
                                        </ul>
                                    </div>
                                    <div class="role-card">
                                        <div class="role-icon">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <h4>محلل ومطور</h4>
                                        <p>تحليل الأنظمة وتطوير حلول لتحسين الكفاءة والجودة</p>
                                        <ul>
                                            <li>تحليل البيانات</li>
                                            <li>تطوير الحلول</li>
                                            <li>تحسين العمليات</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="content-section">
                                <h3>المهارات المطلوبة</h3>
                                <p>يحتاج المهندس الإكلينيكي إلى مجموعة متنوعة من المهارات التقنية والشخصية:</p>

                                <div class="skills-categories">
                                    <div class="skill-category">
                                        <h4><i class="fas fa-cog"></i> المهارات التقنية</h4>
                                        <div class="skills-list">
                                            <span class="skill-tag">الهندسة الطبية الحيوية</span>
                                            <span class="skill-tag">الإلكترونيات</span>
                                            <span class="skill-tag">البرمجة</span>
                                            <span class="skill-tag">تحليل البيانات</span>
                                            <span class="skill-tag">إدارة المشاريع</span>
                                            <span class="skill-tag">ضمان الجودة</span>
                                        </div>
                                    </div>
                                    <div class="skill-category">
                                        <h4><i class="fas fa-users"></i> المهارات الشخصية</h4>
                                        <div class="skills-list">
                                            <span class="skill-tag">التواصل الفعال</span>
                                            <span class="skill-tag">القيادة</span>
                                            <span class="skill-tag">حل المشكلات</span>
                                            <span class="skill-tag">العمل الجماعي</span>
                                            <span class="skill-tag">التفكير النقدي</span>
                                            <span class="skill-tag">إدارة الوقت</span>
                                        </div>
                                    </div>
                                    <div class="skill-category">
                                        <h4><i class="fas fa-stethoscope"></i> المعرفة الطبية</h4>
                                        <div class="skills-list">
                                            <span class="skill-tag">علم التشريح</span>
                                            <span class="skill-tag">علم وظائف الأعضاء</span>
                                            <span class="skill-tag">المصطلحات الطبية</span>
                                            <span class="skill-tag">إجراءات المستشفى</span>
                                            <span class="skill-tag">سلامة المرضى</span>
                                            <span class="skill-tag">العدوى والتحكم</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="content-section">
                                <h3>التحديات المهنية</h3>
                                <p>يواجه المهندس الإكلينيكي تحديات متنوعة في بيئة العمل:</p>

                                <div class="challenges-list">
                                    <div class="challenge-item">
                                        <div class="challenge-icon">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="challenge-content">
                                            <h4>التطور التكنولوجي السريع</h4>
                                            <p>الحاجة للتعلم المستمر ومواكبة التطورات الحديثة</p>
                                            <div class="solution">
                                                <strong>الحل:</strong> التعليم المستمر والمشاركة في المؤتمرات والدورات التدريبية
                                            </div>
                                        </div>
                                    </div>
                                    <div class="challenge-item">
                                        <div class="challenge-icon">
                                            <i class="fas fa-dollar-sign"></i>
                                        </div>
                                        <div class="challenge-content">
                                            <h4>ضغوط الميزانية</h4>
                                            <p>تحقيق التوازن بين الجودة والتكلفة في إدارة التكنولوجيا</p>
                                            <div class="solution">
                                                <strong>الحل:</strong> تطوير استراتيجيات إدارة فعالة وتحليل التكلفة والفائدة
                                            </div>
                                        </div>
                                    </div>
                                    <div class="challenge-item">
                                        <div class="challenge-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div class="challenge-content">
                                            <h4>التعامل مع فرق متعددة التخصصات</h4>
                                            <p>التواصل الفعال مع الأطباء والممرضين والإداريين</p>
                                            <div class="solution">
                                                <strong>الحل:</strong> تطوير مهارات التواصل وفهم احتياجات كل تخصص
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="interactive-element">
                                <h3>تقييم الأدوار</h3>
                                <div class="role-assessment">
                                    <p>قيم أهمية كل دور من أدوار المهندس الإكلينيكي (1-5):</p>
                                    <div class="assessment-items">
                                        <div class="assessment-item">
                                            <label>إدارة التكنولوجيا:</label>
                                            <input type="range" min="1" max="5" value="3" class="role-slider" data-role="technology">
                                            <span class="slider-value">3</span>
                                        </div>
                                        <div class="assessment-item">
                                            <label>ضمان السلامة:</label>
                                            <input type="range" min="1" max="5" value="3" class="role-slider" data-role="safety">
                                            <span class="slider-value">3</span>
                                        </div>
                                        <div class="assessment-item">
                                            <label>التدريب والتعليم:</label>
                                            <input type="range" min="1" max="5" value="3" class="role-slider" data-role="training">
                                            <span class="slider-value">3</span>
                                        </div>
                                        <div class="assessment-item">
                                            <label>التحليل والتطوير:</label>
                                            <input type="range" min="1" max="5" value="3" class="role-slider" data-role="analysis">
                                            <span class="slider-value">3</span>
                                        </div>
                                    </div>
                                    <button class="btn btn-primary assessment-submit">عرض النتائج</button>
                                    <div class="assessment-results" style="display: none;">
                                        <h4>تحليل تقييمك:</h4>
                                        <div id="assessment-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                    <!-- Unit Summary and Assessment -->
                    <section class="unit-summary">
                        <div class="summary-header">
                            <h2>ملخص الوحدة الأولى</h2>
                            <p>مراجعة شاملة للمفاهيم الأساسية التي تعلمتها</p>
                        </div>

                        <div class="key-concepts">
                            <h3>المفاهيم الرئيسية</h3>
                            <div class="concepts-grid">
                                <div class="concept-card">
                                    <i class="fas fa-lightbulb"></i>
                                    <h4>تعريف الهندسة الإكلينيكية</h4>
                                    <p>تطبيق المبادئ الهندسية في البيئة الطبية لضمان سلامة وفعالية التكنولوجيا الطبية</p>
                                </div>
                                <div class="concept-card">
                                    <i class="fas fa-history"></i>
                                    <h4>التطور التاريخي</h4>
                                    <p>من البدايات البسيطة في الأربعينيات إلى التخصص المعقد في عصر الذكاء الاصطناعي</p>
                                </div>
                                <div class="concept-card">
                                    <i class="fas fa-user-tie"></i>
                                    <h4>أدوار المهندس الإكلينيكي</h4>
                                    <p>مدير تكنولوجيا، ضامن سلامة، مدرب، ومحلل أنظمة</p>
                                </div>
                                <div class="concept-card">
                                    <i class="fas fa-cogs"></i>
                                    <h4>المهارات المطلوبة</h4>
                                    <p>مزيج من المهارات التقنية والشخصية والمعرفة الطبية</p>
                                </div>
                            </div>
                        </div>

                        <div class="final-assessment">
                            <h3>التقييم النهائي للوحدة</h3>
                            <div class="assessment-intro">
                                <p>اختبر فهمك الشامل للوحدة الأولى من خلال هذا التقييم التفاعلي</p>
                            </div>

                            <div class="assessment-questions">
                                <div class="question-card">
                                    <h4>السؤال 1: اختيار متعدد</h4>
                                    <p>أي من التالي يُعتبر من المحطات المهمة في تاريخ الهندسة الإكلينيكية؟</p>
                                    <div class="options">
                                        <label><input type="radio" name="final-q1" value="a"> اختراع المجهر عام 1590</label>
                                        <label><input type="radio" name="final-q1" value="b"> تأسيس ACCE عام 1971</label>
                                        <label><input type="radio" name="final-q1" value="c"> اكتشاف البنسلين عام 1928</label>
                                        <label><input type="radio" name="final-q1" value="d"> أول عملية زراعة قلب عام 1967</label>
                                    </div>
                                </div>

                                <div class="question-card">
                                    <h4>السؤال 2: ترتيب الأولويات</h4>
                                    <p>رتب المهام التالية حسب الأولوية في حالة طوارئ طبية:</p>
                                    <div class="sortable-list" id="priority-list">
                                        <div class="sortable-item" data-priority="1">ضمان سلامة المريض</div>
                                        <div class="sortable-item" data-priority="3">توثيق الحادث</div>
                                        <div class="sortable-item" data-priority="2">إصلاح العطل الفني</div>
                                        <div class="sortable-item" data-priority="4">تحليل الأسباب الجذرية</div>
                                    </div>
                                    <button class="btn btn-secondary check-order">تحقق من الترتيب</button>
                                </div>

                                <div class="question-card">
                                    <h4>السؤال 3: دراسة حالة</h4>
                                    <p><strong>الحالة:</strong> مستشفى جديد يريد إنشاء قسم هندسة إكلينيكية. ما هي أهم 3 خطوات يجب اتخاذها؟</p>
                                    <div class="case-study-response">
                                        <textarea placeholder="اكتب إجابتك هنا..." rows="4"></textarea>
                                        <div class="response-hints">
                                            <h5>نصائح للإجابة:</h5>
                                            <ul>
                                                <li>فكر في الهيكل التنظيمي</li>
                                                <li>اعتبر احتياجات التدريب</li>
                                                <li>لا تنس المعايير والسياسات</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="assessment-actions">
                                <button class="btn btn-primary submit-assessment">إرسال التقييم</button>
                                <button class="btn btn-secondary reset-assessment">إعادة تعيين</button>
                            </div>

                            <div class="assessment-results" style="display: none;">
                                <h4>نتائج التقييم</h4>
                                <div class="results-content">
                                    <div class="score-display">
                                        <span class="score-number">0</span>
                                        <span class="score-total">/ 100</span>
                                    </div>
                                    <div class="performance-feedback"></div>
                                    <div class="next-steps">
                                        <h5>الخطوات التالية:</h5>
                                        <ul>
                                            <li>راجع المفاهيم التي تحتاج تحسين</li>
                                            <li>انتقل إلى الوحدة الثانية</li>
                                            <li>طبق ما تعلمته في مشاريع عملية</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="unit-resources">
                            <h3>مصادر إضافية للتعلم</h3>
                            <div class="resources-grid">
                                <div class="resource-card">
                                    <i class="fas fa-book"></i>
                                    <h4>كتب مرجعية</h4>
                                    <ul>
                                        <li>Clinical Engineering Handbook - Dyro</li>
                                        <li>Introduction to Biomedical Engineering - Enderle</li>
                                        <li>Medical Device Technologies - Gartner</li>
                                    </ul>
                                </div>
                                <div class="resource-card">
                                    <i class="fas fa-globe"></i>
                                    <h4>مواقع مفيدة</h4>
                                    <ul>
                                        <li><a href="https://accenet.org" target="_blank">ACCE - الجمعية الأمريكية للهندسة الإكلينيكية</a></li>
                                        <li><a href="https://www.ifmbe.org" target="_blank">IFMBE - الاتحاد الدولي للهندسة الطبية الحيوية</a></li>
                                        <li><a href="https://www.who.int" target="_blank">WHO - منظمة الصحة العالمية</a></li>
                                    </ul>
                                </div>
                                <div class="resource-card">
                                    <i class="fas fa-video"></i>
                                    <h4>محاضرات فيديو</h4>
                                    <ul>
                                        <li>مقدمة في الهندسة الإكلينيكية - MIT</li>
                                        <li>إدارة التكنولوجيا الطبية - Stanford</li>
                                        <li>سلامة المرضى والتكنولوجيا - Johns Hopkins</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </section>

    <!-- Navigation Controls -->
    <section class="unit-navigation">
        <div class="container">
            <div class="nav-controls">
                <a href="../units.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للوحدات
                </a>
                <div class="nav-center">
                    <span class="current-chapter">الفصل 1 من 3</span>
                </div>
                <a href="#chapter2" class="btn btn-primary">
                    الفصل التالي
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- JavaScript Files -->
    <script src="../../js/utils.js"></script>
    <script src="../../js/navigation.js"></script>
    <script src="../../js/animations.js"></script>
    <script src="../../js/main.js"></script>
    <script>
        // Unit-specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize chapter navigation
            const chapterLinks = document.querySelectorAll('.chapter-link');
            const chapters = document.querySelectorAll('.chapter');

            chapterLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetChapter = document.getElementById(targetId);

                    // Update active states
                    chapterLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');

                    // Scroll to chapter
                    targetChapter.scrollIntoView({ behavior: 'smooth' });
                });
            });

            // Quiz functionality
            const quizSubmit = document.querySelector('.quiz-submit');
            if (quizSubmit) {
                quizSubmit.addEventListener('click', function() {
                    const selectedAnswer = document.querySelector('input[name="q1"]:checked');
                    const feedbackDiv = document.querySelector('.quiz-feedback');
                    const feedbackContent = feedbackDiv.querySelector('.feedback-content');

                    if (selectedAnswer) {
                        feedbackDiv.style.display = 'block';
                        if (selectedAnswer.value === 'b') {
                            feedbackContent.innerHTML = '<div class="success-feedback"><i class="fas fa-check-circle"></i> إجابة صحيحة! الهندسة الإكلينيكية تهدف بالفعل إلى إدارة التكنولوجيا الطبية وضمان سلامة المرضى.</div>';
                        } else {
                            feedbackContent.innerHTML = '<div class="error-feedback"><i class="fas fa-times-circle"></i> إجابة خاطئة. الهدف الرئيسي هو إدارة التكنولوجيا الطبية وضمان سلامة المرضى، وليس تصنيع الأجهزة أو بيعها.</div>';
                        }
                    } else {
                        alert('يرجى اختيار إجابة أولاً.');
                    }
                });
            }

            // Work simulator functionality
            const scenarioButtons = document.querySelectorAll('.scenario-btn');
            const scenarios = document.querySelectorAll('.scenario');
            const scenarioFeedback = document.querySelector('.scenario-feedback');

            scenarioButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const scenarioType = this.dataset.scenario;

                    // Hide all scenarios
                    scenarios.forEach(scenario => scenario.style.display = 'none');

                    // Show selected scenario
                    const targetScenario = document.getElementById(scenarioType + '-scenario');
                    if (targetScenario) {
                        targetScenario.style.display = 'block';
                    }

                    // Update button states
                    scenarioButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // Hide previous feedback
                    scenarioFeedback.style.display = 'none';
                });
            });

            // Scenario checking functionality
            const checkScenarioButtons = document.querySelectorAll('.check-scenario');
            checkScenarioButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const scenario = this.closest('.scenario');
                    const scenarioId = scenario.id;
                    const feedbackContent = scenarioFeedback.querySelector('.feedback-content');

                    let feedback = '';

                    if (scenarioId === 'morning-scenario') {
                        const checkedTasks = scenario.querySelectorAll('input[name="morning-tasks"]:checked');
                        const correctTasks = ['check-reports', 'inspect-icu', 'maintenance-schedule'];
                        const selectedValues = Array.from(checkedTasks).map(cb => cb.value);

                        const correctCount = selectedValues.filter(val => correctTasks.includes(val)).length;

                        if (correctCount === 3 && selectedValues.length === 3) {
                            feedback = '<div class="success-feedback"><i class="fas fa-star"></i> ممتاز! اخترت جميع المهام الأساسية للصباح.</div>';
                        } else {
                            feedback = '<div class="warning-feedback"><i class="fas fa-info-circle"></i> المهام الأساسية هي: مراجعة التقارير، فحص العناية المركزة، ومراجعة جدول الصيانة.</div>';
                        }
                    } else if (scenarioId === 'afternoon-scenario') {
                        const step1 = scenario.querySelector('select[name="step1"]').value;
                        const step2 = scenario.querySelector('select[name="step2"]').value;

                        if (step1 === 'safety' && step2 === 'diagnose') {
                            feedback = '<div class="success-feedback"><i class="fas fa-medal"></i> تسلسل ممتاز! السلامة أولاً، ثم التشخيص.</div>';
                        } else {
                            feedback = '<div class="info-feedback"><i class="fas fa-lightbulb"></i> التسلسل الصحيح: 1) التأكد من سلامة المريض 2) تشخيص المشكلة</div>';
                        }
                    } else if (scenarioId === 'emergency-scenario') {
                        const checkedItems = scenario.querySelectorAll('input[name="emergency"]:checked');
                        const score = checkedItems.length;

                        if (score === 4) {
                            feedback = '<div class="success-feedback"><i class="fas fa-shield-alt"></i> استجابة ممتازة! غطيت جميع النقاط الحيوية.</div>';
                        } else if (score >= 2) {
                            feedback = '<div class="warning-feedback"><i class="fas fa-exclamation-triangle"></i> استجابة جيدة، لكن تأكد من تغطية جميع النقاط الأساسية.</div>';
                        } else {
                            feedback = '<div class="error-feedback"><i class="fas fa-times"></i> تحتاج لمراجعة إجراءات الطوارئ.</div>';
                        }
                    }

                    feedbackContent.innerHTML = feedback;
                    scenarioFeedback.style.display = 'block';
                });
            });

            // Interactive timeline functionality
            const timelinePoints = document.querySelectorAll('.timeline-point');
            const timelineDetails = document.getElementById('timeline-details');

            timelinePoints.forEach(point => {
                point.addEventListener('click', function() {
                    const year = this.dataset.year;
                    const event = this.dataset.event;

                    timelineDetails.innerHTML = `
                        <h4>${year}</h4>
                        <p>${event}</p>
                    `;

                    // Update active state
                    timelinePoints.forEach(p => p.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Role assessment functionality
            const roleSliders = document.querySelectorAll('.role-slider');
            const assessmentSubmit = document.querySelector('.assessment-submit');

            roleSliders.forEach(slider => {
                slider.addEventListener('input', function() {
                    const valueSpan = this.nextElementSibling;
                    valueSpan.textContent = this.value;
                });
            });

            if (assessmentSubmit) {
                assessmentSubmit.addEventListener('click', function() {
                    const results = {};
                    roleSliders.forEach(slider => {
                        results[slider.dataset.role] = parseInt(slider.value);
                    });

                    const resultsDiv = document.querySelector('.assessment-results');
                    const feedbackDiv = document.getElementById('assessment-feedback');

                    let feedback = '<ul>';

                    if (results.safety >= 4) {
                        feedback += '<li>ممتاز! تدرك أهمية السلامة في الهندسة الإكلينيكية.</li>';
                    }
                    if (results.technology >= 4) {
                        feedback += '<li>جيد! إدارة التكنولوجيا هي جوهر عمل المهندس الإكلينيكي.</li>';
                    }
                    if (results.training >= 3) {
                        feedback += '<li>التدريب والتعليم مهم لنشر المعرفة والممارسات الآمنة.</li>';
                    }
                    if (results.analysis >= 3) {
                        feedback += '<li>التحليل والتطوير يساعد في تحسين الأنظمة باستمرار.</li>';
                    }

                    feedback += '</ul>';

                    feedbackDiv.innerHTML = feedback;
                    resultsDiv.style.display = 'block';
                });
            }

            // Progress tracking
            function updateProgress() {
                // This would typically connect to a backend system
                // For now, we'll simulate progress tracking
                const completedSections = document.querySelectorAll('.chapter.completed').length;
                const totalSections = document.querySelectorAll('.chapter').length;
                const progressPercentage = Math.round((completedSections / totalSections) * 100);

                const progressFill = document.querySelector('.progress-fill');
                const progressText = document.querySelector('.progress-percentage');

                if (progressFill && progressText) {
                    progressFill.style.width = progressPercentage + '%';
                    progressText.textContent = progressPercentage + '%';
                }
            }

            // Final assessment functionality
            const submitAssessment = document.querySelector('.submit-assessment');
            const resetAssessment = document.querySelector('.reset-assessment');
            const assessmentResults = document.querySelector('.assessment-results');

            if (submitAssessment) {
                submitAssessment.addEventListener('click', function() {
                    let score = 0;
                    let totalQuestions = 3;
                    let feedback = [];

                    // Question 1 - Multiple choice
                    const q1Answer = document.querySelector('input[name="final-q1"]:checked');
                    if (q1Answer && q1Answer.value === 'b') {
                        score += 33;
                        feedback.push('✓ السؤال الأول: إجابة صحيحة');
                    } else {
                        feedback.push('✗ السؤال الأول: الإجابة الصحيحة هي تأسيس ACCE عام 1971');
                    }

                    // Question 2 - Priority ordering
                    const priorityItems = document.querySelectorAll('#priority-list .sortable-item');
                    let correctOrder = true;
                    priorityItems.forEach((item, index) => {
                        const expectedPriority = parseInt(item.dataset.priority);
                        if (expectedPriority !== index + 1) {
                            correctOrder = false;
                        }
                    });

                    if (correctOrder) {
                        score += 33;
                        feedback.push('✓ السؤال الثاني: ترتيب صحيح للأولويات');
                    } else {
                        feedback.push('✗ السؤال الثاني: الترتيب الصحيح هو: سلامة المريض، إصلاح العطل، التوثيق، تحليل الأسباب');
                    }

                    // Question 3 - Case study (basic check for content)
                    const caseStudyResponse = document.querySelector('.case-study-response textarea').value.trim();
                    if (caseStudyResponse.length > 50) {
                        score += 34;
                        feedback.push('✓ السؤال الثالث: إجابة مفصلة ومناسبة');
                    } else {
                        feedback.push('✗ السؤال الثالث: يحتاج إجابة أكثر تفصيلاً');
                    }

                    // Display results
                    const scoreDisplay = assessmentResults.querySelector('.score-number');
                    const performanceFeedback = assessmentResults.querySelector('.performance-feedback');

                    scoreDisplay.textContent = score;

                    let performanceMessage = '';
                    if (score >= 80) {
                        performanceMessage = '<div class="excellent-performance"><i class="fas fa-trophy"></i> أداء ممتاز! لديك فهم قوي للمفاهيم الأساسية.</div>';
                    } else if (score >= 60) {
                        performanceMessage = '<div class="good-performance"><i class="fas fa-thumbs-up"></i> أداء جيد! مع بعض المراجعة ستكون مستعداً للوحدة التالية.</div>';
                    } else {
                        performanceMessage = '<div class="needs-improvement"><i class="fas fa-book-reader"></i> يُنصح بمراجعة المحتوى مرة أخرى قبل الانتقال للوحدة التالية.</div>';
                    }

                    performanceFeedback.innerHTML = performanceMessage + '<ul>' + feedback.map(f => '<li>' + f + '</li>').join('') + '</ul>';
                    assessmentResults.style.display = 'block';

                    // Scroll to results
                    assessmentResults.scrollIntoView({ behavior: 'smooth' });
                });
            }

            if (resetAssessment) {
                resetAssessment.addEventListener('click', function() {
                    // Reset all form inputs
                    document.querySelectorAll('.assessment-questions input, .assessment-questions select, .assessment-questions textarea').forEach(input => {
                        if (input.type === 'radio' || input.type === 'checkbox') {
                            input.checked = false;
                        } else {
                            input.value = '';
                        }
                    });

                    // Hide results
                    assessmentResults.style.display = 'none';
                });
            }

            // Sortable functionality for priority question
            const priorityList = document.getElementById('priority-list');
            if (priorityList) {
                let draggedElement = null;

                priorityList.addEventListener('dragstart', function(e) {
                    draggedElement = e.target;
                    e.target.style.opacity = '0.5';
                });

                priorityList.addEventListener('dragend', function(e) {
                    e.target.style.opacity = '';
                    draggedElement = null;
                });

                priorityList.addEventListener('dragover', function(e) {
                    e.preventDefault();
                });

                priorityList.addEventListener('drop', function(e) {
                    e.preventDefault();
                    if (draggedElement && e.target.classList.contains('sortable-item')) {
                        const allItems = Array.from(priorityList.children);
                        const draggedIndex = allItems.indexOf(draggedElement);
                        const targetIndex = allItems.indexOf(e.target);

                        if (draggedIndex < targetIndex) {
                            priorityList.insertBefore(draggedElement, e.target.nextSibling);
                        } else {
                            priorityList.insertBefore(draggedElement, e.target);
                        }
                    }
                });

                // Make items draggable
                priorityList.querySelectorAll('.sortable-item').forEach(item => {
                    item.draggable = true;
                });
            }

            // Check order button functionality
            const checkOrderBtn = document.querySelector('.check-order');
            if (checkOrderBtn) {
                checkOrderBtn.addEventListener('click', function() {
                    const items = document.querySelectorAll('#priority-list .sortable-item');
                    let isCorrect = true;

                    items.forEach((item, index) => {
                        const expectedPriority = parseInt(item.dataset.priority);
                        if (expectedPriority !== index + 1) {
                            isCorrect = false;
                        }
                    });

                    if (isCorrect) {
                        alert('ترتيب صحيح! أحسنت.');
                    } else {
                        alert('ترتيب خاطئ. الترتيب الصحيح هو: سلامة المريض، إصلاح العطل، التوثيق، تحليل الأسباب.');
                    }
                });
            }

            // Mark sections as read when scrolled
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const chapterId = entry.target.id;
                        const chapterLink = document.querySelector(`a[href="#${chapterId}"]`);
                        if (chapterLink) {
                            chapterLink.classList.add('visited');
                        }
                    }
                });
            }, { threshold: 0.5 });

            chapters.forEach(chapter => {
                observer.observe(chapter);
            });
        });
    </script>
</body>
</html>
