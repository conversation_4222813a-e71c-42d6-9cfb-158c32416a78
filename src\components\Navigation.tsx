import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, BookText, Shield, Cog, Users, Globe, FileText } from 'lucide-react';

const Navigation: React.FC = () => {
  const location = useLocation();
  
  const navItems = [
    { path: '/', label: 'الرئيسية', icon: Home },
    { path: '/unit1', label: 'الوحدة الأولى: مدخل إلى الهندسة الإكلينيكية', icon: BookText },
    { path: '/unit2', label: 'الوحدة الثانية: ضمان الجودة وسلامة المرضى', icon: Shield },
    { path: '/unit3', label: 'الوحدة الثالثة: إدارة دورة حياة التكنولوجيا', icon: Cog },
    { path: '/unit4', label: 'الوحدة الرابعة: إدارة قسم الهندسة الإكلينيكية', icon: Users },
    { path: '/unit5', label: 'الوحدة الخامسة: آفاق عالمية وتوجهات مستقبلية', icon: Globe },
    { path: '/glossary', label: 'مسرد المصطلحات', icon: FileText },
  ];

  return (
    <nav className="bg-white shadow-md border-b border-blue-100 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex overflow-x-auto scrollbar-hide">
          {navItems.map(({ path, label, icon: Icon }) => (
            <Link
              key={path}
              to={path}
              className={`flex items-center px-4 py-3 text-sm font-medium whitespace-nowrap transition-colors ${
                location.pathname === path
                  ? 'text-blue-600 bg-blue-50 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
              }`}
            >
              <Icon className="h-4 w-4 ml-2 flex-shrink-0" />
              <span className="hidden md:inline">{label}</span>
              <span className="md:hidden">
                {label.includes('الوحدة') ? label.split(':')[0] : label}
              </span>
            </Link>
          ))}
        </div>
      </div>
    </nav>
  );
};

export default Navigation;