import React, { useState } from 'react';
import { Search, BookOpen } from 'lucide-react';

const GlossaryPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const glossaryTerms = [
    { term: 'ACCE', definition: 'الكلية الأمريكية للهندسة الإكلينيكية، وهي منظمة مهنية رائدة في الولايات المتحدة' },
    { term: 'AAMI', definition: 'جمعية تطوير الأجهزة الطبية، وهي منظمة رئيسية تضع المعايير والممارسات الموصى بها' },
    { term: 'Asset Management', definition: 'إدارة الأصول: العملية المنهجية لإدارة الأصول المادية طوال دورة حياتها لتعظيم قيمتها' },
    { term: 'BMET', definition: 'فني الأجهزة الطبية الحيوية، وهو المحترف المسؤول عن الصيانة العملية والإصلاح والمعايرة' },
    { term: 'Calibration', definition: 'المعايرة: عملية مقارنة قراءة جهاز قياس بمعيار معروف وأكثر دقة' },
    { term: 'CCE', definition: 'مهندس إكلينيكي معتمد، وهي شهادة مهنية تثبت مستوى عالٍ من الكفاءة والخبرة' },
    { term: 'CE', definition: 'الهندسة الإكلينيكية' },
    { term: 'CMMS', definition: 'نظام إدارة الصيانة المحوسب، وهو برنامج يستخدم لتنظيم وتوثيق جميع أنشطة إدارة التكنولوجيا الطبية' },
    { term: 'Cybersecurity', definition: 'الأمن السيبراني: حماية الأنظمة والشبكات والأجهزة المتصلة من الهجمات الرقمية' },
    { term: 'Decommissioning', definition: 'الإخراج من الخدمة: العملية الرسمية والمنظمة لإزالة جهاز من الخدمة والتخلص منه بطريقة آمنة' },
    { term: 'Downtime', definition: 'فترة التوقف: الفترة الزمنية التي يكون فيها الجهاز خارج الخدمة بسبب عطل' },
    { term: 'EHR', definition: 'السجل الصحي الإلكتروني، وهو سجل رقمي لمعلومات المريض الصحية' },
    { term: 'FMEA', definition: 'تحليل أنماط الفشل وتأثيراتها، وهو أداة استباقية لتقييم المخاطر' },
    { term: 'HTA', definition: 'تقييم التكنولوجيا الصحية، وهو التقييم المنهجي للتكنولوجيا الطبية من جوانب متعددة' },
    { term: 'IEC', definition: 'اللجنة الكهروتقنية الدولية، وهي منظمة عالمية تضع معايير دولية للتقنيات الكهربائية والإلكترونية' },
    { term: 'IoMT', definition: 'إنترنت الأشياء الطبية، وهي شبكة من الأجهزة الطبية المتصلة التي تجمع وتتبادل البيانات' },
    { term: 'ISO', definition: 'المنظمة الدولية للتوحيد القياسي، وهي منظمة تضع معايير دولية في مختلف المجالات' },
    { term: 'JCI', definition: 'اللجنة الدولية المشتركة، وهي هيئة رائدة في اعتماد مؤسسات الرعاية الصحية حول العالم' },
    { term: 'KPI', definition: 'مؤشر أداء رئيسي، وهو مقياس كمي يستخدم لتقييم أداء وفعالية عملية أو قسم' },
    { term: 'LMICs', definition: 'الدول ذات الدخل المنخفض والمتوسط' },
    { term: 'MTBF', definition: 'متوسط الوقت بين الأعطال، وهو مؤشر على موثوقية الجهاز' },
    { term: 'OEM', definition: 'الشركة المصنعة الأصلية للجهاز' },
    { term: 'Outsourcing', definition: 'الاستعانة بمصادر خارجية: إسناد بعض العمليات أو الخدمات إلى شركة خارجية' },
    { term: 'PACS', definition: 'نظام أرشفة الصور والاتصالات، ويستخدم لتخزين واسترجاع الصور الطبية مثل الأشعة' },
    { term: 'PM', definition: 'الصيانة الوقائية: الأنشطة المجدولة التي تتم لمنع الأعطال' },
    { term: 'RCA', definition: 'تحليل السبب الجذري، وهو أداة تفاعلية للتحقيق في الحوادث' },
    { term: 'Risk Management', definition: 'إدارة المخاطر: العملية المنهجية لتحديد وتقييم والتحكم في المخاطر' },
    { term: 'TCO', definition: 'التكلفة الإجمالية للملكية، وهي تشمل جميع التكاليف المرتبطة بامتلاك وتشغيل جهاز على مدى عمره' },
    { term: 'Uptime', definition: 'وقت التشغيل: النسبة المئوية للوقت الذي يكون فيه الجهاز متاحًا للعمل' },
    { term: 'WHO', definition: 'منظمة الصحة العالمية' }
  ];

  const filteredTerms = glossaryTerms.filter(item =>
    item.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.definition.includes(searchTerm)
  );

  return (
    <div className="max-w-5xl mx-auto">
      <div className="bg-gray-700 text-white rounded-t-2xl p-8">
        <div className="flex items-center mb-4">
          <BookOpen className="h-8 w-8 ml-3" />
          <h1 className="text-3xl font-bold">مسرد المصطلحات الشامل</h1>
        </div>
        <p className="text-gray-200 text-lg">
          مرجع سريع للمصطلحات والمختصرات الرئيسية في الهندسة الإكلينيكية
        </p>
      </div>

      <div className="bg-white rounded-b-2xl shadow-lg p-8">
        {/* البحث */}
        <div className="mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="البحث في المصطلحات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* نتائج البحث */}
        <div className="space-y-4">
          {filteredTerms.length > 0 ? (
            filteredTerms.map((item, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors">
                <div className="flex items-start">
                  <div className="bg-blue-600 text-white rounded-lg px-3 py-1 text-sm font-bold ml-4 mt-1 flex-shrink-0">
                    {item.term}
                  </div>
                  <div className="flex-1">
                    <p className="text-gray-800 leading-relaxed">{item.definition}</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-12">
              <div className="bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Search className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">لم يتم العثور على نتائج</h3>
              <p className="text-gray-600">جرب البحث بكلمات مفتاحية مختلفة</p>
            </div>
          )}
        </div>

        {/* إحصائيات */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="text-center">
            <p className="text-gray-600">
              عرض {filteredTerms.length} من أصل {glossaryTerms.length} مصطلح
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GlossaryPage;