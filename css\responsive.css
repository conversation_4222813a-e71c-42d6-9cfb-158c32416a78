/* ===== RESPONSIVE DESIGN ===== */

/* Breakpoints */
/* 
  xs: 0px - 479px (Mobile Portrait)
  sm: 480px - 767px (Mobile Landscape)
  md: 768px - 1023px (Tablet)
  lg: 1024px - 1199px (Desktop)
  xl: 1200px+ (Large Desktop)
*/

/* Extra Small Devices (Mobile Portrait) */
@media (max-width: 479px) {
  /* Typography */
  html { font-size: 12px; }
  
  h1 { font-size: 1.5rem; }
  h2 { font-size: 1.375rem; }
  h3 { font-size: 1.25rem; }
  h4 { font-size: 1.125rem; }
  
  .section-title { font-size: 1.5rem; }
  .section-subtitle { font-size: 1rem; }
  
  /* Layout */
  .container { 
    padding: 0 var(--spacing-sm); 
    max-width: 100%;
  }
  
  section { padding: var(--spacing-xl) 0; }
  
  /* Navigation */
  .nav-container { 
    padding: 0 var(--spacing-sm); 
    height: 60px;
  }
  
  .nav-logo {
    font-size: 1rem;
    gap: var(--spacing-sm);
  }
  
  .logo-img {
    width: 30px;
    height: 30px;
  }
  
  .nav-menu {
    top: 60px;
    padding: var(--spacing-lg);
  }
  
  /* Hero Section */
  .hero {
    min-height: 80vh;
    padding-top: 60px;
  }
  
  .hero-container {
    padding: 0 var(--spacing-sm);
  }
  
  .hero-title {
    font-size: 1.75rem;
    margin-bottom: var(--spacing-md);
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 0.875rem;
    width: 100%;
    justify-content: center;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
  
  .stat-number { font-size: 1.5rem; }
  .stat-label { font-size: 0.75rem; }
  
  /* Cards */
  .card { padding: var(--spacing-lg); }
  
  /* Grids */
  .objectives-grid,
  .features-list {
    gap: var(--spacing-md);
  }
  
  .objective-card,
  .feature-item {
    padding: var(--spacing-md);
  }
  
  .objective-card i { font-size: 1.5rem; }
  .feature-item i { font-size: 1.25rem; }
}

/* Small Devices (Mobile Landscape) */
@media (min-width: 480px) and (max-width: 767px) {
  /* Typography */
  html { font-size: 13px; }
  
  .hero-title { font-size: 2rem; }
  .section-title { font-size: 1.75rem; }
  
  /* Layout */
  .container { padding: 0 var(--spacing-md); }
  
  /* Hero */
  .hero-buttons {
    flex-direction: row;
    justify-content: center;
  }
  
  .btn {
    width: auto;
  }
  
  .hero-stats {
    flex-direction: row;
    justify-content: center;
  }
  
  /* Grids */
  .objectives-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Medium Devices (Tablets) */
@media (min-width: 768px) and (max-width: 1023px) {
  /* Typography */
  html { font-size: 14px; }
  
  .hero-title { font-size: 2.5rem; }
  .section-title { font-size: 2rem; }
  
  /* Navigation */
  .nav-menu {
    display: flex;
    position: static;
    background: transparent;
    flex-direction: row;
    padding: 0;
    box-shadow: none;
    transform: none;
    opacity: 1;
    visibility: visible;
    gap: var(--spacing-lg);
  }
  
  .nav-toggle { display: none; }
  
  /* Hero */
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }
  
  .hero-image {
    order: -1;
    max-width: 500px;
    margin: 0 auto;
  }
  
  /* About */
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }
  
  .objectives-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* Floating cards - hide on tablets */
  .floating-card {
    display: none;
  }
}

/* Large Devices (Desktop) */
@media (min-width: 1024px) and (max-width: 1199px) {
  /* Typography */
  html { font-size: 15px; }
  
  /* Hero */
  .hero-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
  }
  
  .hero-title { font-size: 3rem; }
  
  /* About */
  .about-content {
    grid-template-columns: 1fr 1fr;
  }
  
  .objectives-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Extra Large Devices (Large Desktop) */
@media (min-width: 1200px) {
  /* Typography */
  html { font-size: 16px; }
  
  .hero-title { font-size: 3.5rem; }
  
  /* Layout */
  .container { max-width: 1200px; }
  
  /* Hero */
  .hero-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
  }
  
  /* About */
  .objectives-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Landscape Orientation */
@media (orientation: landscape) and (max-height: 600px) {
  .hero {
    min-height: 100vh;
    padding: var(--spacing-xl) 0;
  }
  
  .hero-title { font-size: 2rem; }
  .hero-subtitle { font-size: 1.125rem; }
  
  .hero-stats {
    flex-direction: row;
    gap: var(--spacing-lg);
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo-img,
  .hero-img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-300: #6b7280;
    --gray-400: #9ca3af;
    --gray-500: #d1d5db;
    --gray-600: #e5e7eb;
    --gray-700: #f3f4f6;
    --gray-800: #f9fafb;
    --gray-900: #ffffff;
  }
  
  body {
    background-color: var(--gray-50);
    color: var(--gray-800);
  }
  
  .navbar {
    background: rgba(31, 41, 55, 0.95);
    border-bottom-color: var(--gray-200);
  }
  
  .card {
    background: var(--gray-100);
  }
  
  .hero {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .hero-content,
  .about-content {
    animation: none;
  }
  
  .floating-card {
    animation: none;
    position: static;
    display: inline-flex;
    margin: var(--spacing-sm);
  }
  
  .scroll-animate {
    opacity: 1;
    transform: none;
    transition: none;
  }
}

/* Print Styles */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .navbar,
  .nav-toggle,
  .hero-buttons,
  .floating-card,
  .btn {
    display: none !important;
  }
  
  .hero {
    min-height: auto;
    padding: var(--spacing-lg) 0;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .hero-title {
    font-size: 2rem;
    color: black !important;
  }
  
  .section-title {
    font-size: 1.5rem;
    color: black !important;
  }
  
  .card {
    border: 1px solid #ccc !important;
    margin-bottom: var(--spacing-lg);
  }
  
  a {
    color: black !important;
    text-decoration: underline !important;
  }
  
  .about-content {
    grid-template-columns: 1fr;
  }
  
  .objectives-grid,
  .features-list {
    grid-template-columns: 1fr;
  }
}

/* Accessibility */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #0000ff;
    --secondary-color: #008000;
    --gray-600: #000000;
    --gray-700: #000000;
    --gray-800: #000000;
    --gray-900: #000000;
  }
  
  .btn {
    border: 2px solid currentColor;
  }
  
  .card {
    border: 1px solid var(--gray-400);
  }
}

/* Focus Visible */
@media (prefers-reduced-motion: no-preference) {
  .nav-link:focus-visible,
  .btn:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    transition: outline var(--transition-fast);
  }
}
