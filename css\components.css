/* ===== COMPONENTS STYLES ===== */

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-200);
  z-index: 1000;
  transition: all var(--transition-normal);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-md);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--gray-900);
}

.logo-img {
  width: 40px;
  height: 40px;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav-link {
  font-weight: 500;
  color: var(--gray-700);
  transition: color var(--transition-fast);
  position: relative;
  padding: var(--spacing-sm) 0;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.bar {
  width: 25px;
  height: 3px;
  background: var(--gray-700);
  transition: all var(--transition-fast);
  border-radius: 2px;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--blue-50) 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/assets/images/pattern.svg') repeat;
  opacity: 0.05;
  z-index: 1;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.hero-text {
  animation: fadeInUp 1s ease-out;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
}

.title-main {
  display: block;
  color: var(--gray-900);
}

.title-highlight {
  display: block;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--spacing-md);
}

.hero-description {
  font-size: 1.125rem;
  color: var(--gray-600);
  margin-bottom: var(--spacing-xl);
  line-height: 1.7;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.hero-stats {
  display: flex;
  gap: var(--spacing-xl);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
}

.hero-image {
  position: relative;
  animation: fadeInRight 1s ease-out 0.3s both;
}

.hero-img {
  width: 100%;
  height: auto;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
}

.hero-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-card {
  position: absolute;
  background: white;
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 600;
  color: var(--gray-800);
  animation: float 3s ease-in-out infinite;
}

.floating-card:nth-child(1) {
  top: 20%;
  right: -10%;
  animation-delay: 0s;
}

.floating-card:nth-child(2) {
  top: 50%;
  left: -15%;
  animation-delay: 1s;
}

.floating-card:nth-child(3) {
  bottom: 20%;
  right: -5%;
  animation-delay: 2s;
}

.floating-card i {
  color: var(--primary-color);
  font-size: 1.25rem;
}

/* About Section */
.about {
  background: white;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: start;
}

.about-text h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  font-size: 1.5rem;
}

.objectives-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.objective-card {
  background: var(--gray-50);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  text-align: center;
  transition: all var(--transition-normal);
}

.objective-card:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-5px);
}

.objective-card i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  transition: color var(--transition-normal);
}

.objective-card:hover i {
  color: white;
}

.objective-card h4 {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-sm);
}

.objective-card p {
  font-size: 0.875rem;
  margin: 0;
}

.about-features h3 {
  color: var(--secondary-color);
  margin-bottom: var(--spacing-xl);
  font-size: 1.5rem;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.feature-item:hover {
  background: var(--secondary-color);
  color: white;
  transform: translateX(-10px);
}

.feature-item i {
  font-size: 1.5rem;
  color: var(--secondary-color);
  margin-top: var(--spacing-xs);
  transition: color var(--transition-normal);
}

.feature-item:hover i {
  color: white;
}

.feature-item h4 {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-xs);
}

.feature-item p {
  font-size: 0.875rem;
  margin: 0;
}

/* Units Section */
.units {
  background: var(--gray-50);
  padding: var(--spacing-3xl) 0;
}

.units-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
}

.unit-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
}

.unit-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.unit-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.unit-number {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.875rem;
}

.unit-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.unit-content {
  padding: var(--spacing-xl);
}

.unit-content h3 {
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
  font-size: 1.25rem;
}

.unit-content p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.unit-meta {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.unit-meta span {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--gray-500);
  font-size: 0.875rem;
}

.unit-footer {
  padding: 0 var(--spacing-xl) var(--spacing-xl);
}

.unit-btn {
  width: 100%;
  justify-content: center;
}

.glossary-card .unit-header {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
}

/* Resources Section */
.resources {
  background: white;
  padding: var(--spacing-3xl) 0;
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
}

.resource-card {
  background: var(--gray-50);
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  text-align: center;
  transition: all var(--transition-normal);
}

.resource-card:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-5px);
}

.resource-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  transition: color var(--transition-normal);
}

.resource-card:hover .resource-icon {
  color: white;
}

.resource-card h4 {
  margin-bottom: var(--spacing-md);
  color: inherit;
}

.resource-card p {
  margin-bottom: var(--spacing-lg);
  color: inherit;
  opacity: 0.8;
}

/* Contact Section */
.contact {
  background: var(--gray-50);
  padding: var(--spacing-3xl) 0;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  margin-top: var(--spacing-2xl);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.contact-icon {
  background: var(--primary-color);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.contact-details h4 {
  color: var(--gray-900);
  margin-bottom: var(--spacing-sm);
}

.contact-details p {
  color: var(--gray-600);
  margin: 0;
}

.contact-form {
  background: white;
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--gray-700);
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: white;
    flex-direction: column;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-toggle.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    justify-content: center;
  }

  .about-content {
    grid-template-columns: 1fr;
  }

  .objectives-grid {
    grid-template-columns: 1fr;
  }

  .floating-card {
    display: none;
  }

  .units-grid {
    grid-template-columns: 1fr;
  }

  .resources-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .resources-grid {
    grid-template-columns: 1fr;
  }
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 1000;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.modal.modal-visible {
  opacity: 1;
}

.modal.modal-closing {
  opacity: 0;
}

.modal-content {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  max-width: 800px;
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: transform var(--transition-normal);
}

.modal-visible .modal-content {
  transform: scale(1) translateY(0);
}

.modal-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.modal-body {
  padding: var(--spacing-xl);
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  background: var(--gray-50);
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

/* Unit Modal Specific Styles */
.unit-overview {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.unit-stats {
  display: flex;
  gap: var(--spacing-xl);
  justify-content: center;
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.unit-stats .stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.unit-stats .stat i {
  color: var(--primary-color);
  font-size: 1.5rem;
}

.unit-stats .stat span {
  font-weight: 600;
  color: var(--gray-700);
}

.unit-description h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.unit-chapters h3 {
  color: var(--secondary-color);
  margin-bottom: var(--spacing-lg);
}

.chapters-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.chapter-item {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.chapter-item:hover {
  background: var(--primary-color);
  color: white;
  transform: translateX(-5px);
}

.chapter-number {
  background: var(--primary-color);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
  transition: background-color var(--transition-normal);
}

.chapter-item:hover .chapter-number {
  background: white;
  color: var(--primary-color);
}

.chapter-content {
  flex: 1;
}

.chapter-content h4 {
  margin-bottom: var(--spacing-sm);
  font-size: 1.125rem;
}

.chapter-content p {
  margin-bottom: var(--spacing-sm);
  opacity: 0.8;
}

.chapter-duration {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  opacity: 0.7;
}

.unit-objectives h3 {
  color: var(--accent-color);
  margin-bottom: var(--spacing-lg);
}

.unit-objectives ul {
  list-style: none;
  padding: 0;
}

.unit-objectives li {
  position: relative;
  padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-xl);
  margin-bottom: var(--spacing-sm);
}

.unit-objectives li::before {
  content: '✓';
  position: absolute;
  right: 0;
  top: var(--spacing-sm);
  color: var(--secondary-color);
  font-weight: bold;
}

/* Glossary Modal Specific Styles */
.glossary-search {
  position: relative;
  margin-bottom: var(--spacing-xl);
}

.glossary-search input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  transition: border-color var(--transition-fast);
}

.glossary-search input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.glossary-search i {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
}

.glossary-content {
  max-height: 400px;
  overflow-y: auto;
}

.glossary-section {
  margin-bottom: var(--spacing-xl);
}

.glossary-section h3 {
  background: var(--primary-color);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  font-size: 1.25rem;
  text-align: center;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.term-item {
  padding: var(--spacing-lg);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-md);
  transition: all var(--transition-normal);
}

.term-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.term-item h4 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.term-item .english {
  font-size: 0.875rem;
  color: var(--gray-500);
  font-weight: normal;
  font-style: italic;
}

.term-item p {
  margin: 0;
  color: var(--gray-700);
  line-height: 1.6;
}

.term-item mark {
  background: var(--accent-color);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
}

/* Modal Responsive Design */
@media (max-width: 768px) {
  .modal {
    padding: var(--spacing-md);
  }

  .modal-content {
    max-height: 95vh;
  }

  .modal-header {
    padding: var(--spacing-lg);
  }

  .modal-header h2 {
    font-size: 1.25rem;
  }

  .modal-body {
    padding: var(--spacing-lg);
  }

  .modal-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    flex-direction: column;
  }

  .unit-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .chapter-item {
    flex-direction: column;
    text-align: center;
  }

  .chapter-number {
    align-self: center;
  }
}

/* Footer Styles */
.footer {
  background: linear-gradient(135deg, var(--gray-900), var(--gray-800));
  color: white;
  padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--spacing-3xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-section h4 {
  color: white;
  margin-bottom: var(--spacing-lg);
  font-size: 1.25rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.footer-logo-img {
  width: 40px;
  height: 40px;
}

.footer-section p {
  color: var(--gray-300);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.footer-stats {
  display: flex;
  gap: var(--spacing-xl);
}

.footer-stat {
  text-align: center;
}

.footer-stat .stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.footer-stat .stat-label {
  font-size: 0.875rem;
  color: var(--gray-400);
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: var(--gray-300);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-contact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.footer-contact .contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--gray-300);
  font-size: 0.875rem;
}

.footer-contact .contact-item i {
  color: var(--primary-color);
  width: 16px;
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--gray-700);
  color: white;
  border-radius: 50%;
  text-decoration: none;
  transition: all var(--transition-normal);
}

.social-link:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
}

.footer-bottom {
  border-top: 1px solid var(--gray-700);
  padding-top: var(--spacing-lg);
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright p {
  color: var(--gray-400);
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-bottom-links a {
  color: var(--gray-400);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color var(--transition-fast);
}

.footer-bottom-links a:hover {
  color: var(--primary-color);
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: var(--spacing-xl);
  left: var(--spacing-xl);
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  z-index: 1000;
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-slow);
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content h3 {
  margin-bottom: var(--spacing-md);
  font-size: 1.5rem;
}

.loading-content p {
  opacity: 0.8;
  margin: 0;
}

/* Notification Container */
.notification-container {
  position: fixed;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.notification {
  background: white;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border-right: 4px solid var(--primary-color);
  transform: translateX(100%);
  opacity: 0;
  transition: all var(--transition-normal);
  max-width: 300px;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification.notification-success {
  border-right-color: var(--secondary-color);
}

.notification.notification-error {
  border-right-color: #ef4444;
}

.notification.notification-warning {
  border-right-color: var(--accent-color);
}

/* Search Styles */
.nav-search {
  position: relative;
  margin-right: var(--spacing-lg);
}

.nav-search input {
  padding: var(--spacing-sm) var(--spacing-xl) var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  width: 200px;
  transition: all var(--transition-fast);
}

.nav-search input:focus {
  outline: none;
  border-color: var(--primary-color);
  width: 250px;
}

.nav-search i {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
}

.search-highlight {
  background: var(--accent-color) !important;
  color: white !important;
  padding: 2px 4px;
  border-radius: 3px;
  animation: pulse 2s infinite;
}

/* Learning Environment */
.learning-environment {
  background: white;
}

.learning-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.learning-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.close-learning {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.close-learning:hover {
  background: rgba(255, 255, 255, 0.1);
}

.learning-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

.learning-sidebar {
  width: 300px;
  background: var(--gray-50);
  padding: var(--spacing-xl);
  border-left: 1px solid var(--gray-200);
}

.learning-sidebar h3 {
  margin-bottom: var(--spacing-lg);
  color: var(--gray-900);
}

.chapter-list {
  list-style: none;
  padding: 0;
}

.chapter-list .chapter-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-bottom: var(--spacing-sm);
}

.chapter-list .chapter-item:hover {
  background: var(--gray-100);
}

.chapter-list .chapter-item.active {
  background: var(--primary-color);
  color: white;
}

.chapter-list .chapter-item i {
  color: var(--primary-color);
}

.chapter-list .chapter-item.active i {
  color: white;
}

.learning-main {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
}

.content-area h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
}

.progress-section {
  background: var(--gray-50);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-xl);
}

.progress-section h4 {
  margin-bottom: var(--spacing-md);
  color: var(--gray-900);
}

.learning-footer {
  background: var(--gray-50);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Responsive Footer */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .footer-stats {
    justify-content: center;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .nav-search {
    margin-right: 0;
    margin-top: var(--spacing-md);
  }

  .nav-search input {
    width: 100%;
  }

  .learning-content {
    flex-direction: column;
  }

  .learning-sidebar {
    width: 100%;
  }

  .learning-footer {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}

/* Unit Content Specific Styles */
.unit-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 4rem 0 2rem;
  position: relative;
  overflow: hidden;
}

.unit-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('../assets/images/pattern.svg') repeat;
  opacity: 0.1;
}

.unit-header-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.unit-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.unit-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.unit-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.unit-meta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.unit-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
}

.unit-progress-section {
  padding: 2rem 0;
  background: var(--light-bg);
}

.progress-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.progress-percentage {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.progress-bar {
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  padding: 3rem 0;
}

.content-sidebar {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.sidebar-content {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chapter-nav {
  margin-bottom: 2rem;
}

.chapter-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.5rem;
  text-decoration: none;
  color: var(--text-color);
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.chapter-link:hover,
.chapter-link.active {
  background: var(--primary-color);
  color: white;
}

.chapter-number {
  width: 2rem;
  height: 2rem;
  background: var(--light-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.chapter-link.active .chapter-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.chapter-title {
  flex: 1;
  font-size: 0.9rem;
  font-weight: 500;
}

.chapter-status {
  opacity: 0.5;
}

.chapter-link.visited .chapter-status {
  color: var(--success-color);
  opacity: 1;
}

.btn-block {
  width: 100%;
  margin-bottom: 0.5rem;
}

.unit-navigation-sidebar h5 {
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.unit-nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  text-decoration: none;
  color: var(--text-secondary);
  font-size: 0.85rem;
  transition: all 0.3s ease;
  margin-bottom: 0.25rem;
}

.unit-nav-link:hover {
  background: var(--light-bg);
  color: var(--primary-color);
}

.main-content {
  max-width: none;
}

.chapter {
  margin-bottom: 4rem;
  scroll-margin-top: 2rem;
}

.chapter-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--light-bg);
}

.chapter-badge {
  display: inline-block;
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.chapter h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.chapter-meta {
  display: flex;
  gap: 1.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.chapter-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.learning-objectives {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border: 1px solid #0ea5e9;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
}

.learning-objectives h3 {
  color: #0369a1;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.learning-objectives h3::before {
  content: '🎯';
  font-size: 1.2rem;
}

.learning-objectives ul {
  list-style: none;
  padding: 0;
}

.learning-objectives li {
  padding: 0.5rem 0;
  padding-right: 1.5rem;
  position: relative;
}

.learning-objectives li::before {
  content: '✓';
  position: absolute;
  right: 0;
  color: #0ea5e9;
  font-weight: bold;
}

.content-section {
  margin-bottom: 3rem;
}

.content-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.content-section h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.definition-box {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
  border-right: 4px solid var(--primary-color);
}

.definition-box h4 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.definition-box cite {
  display: block;
  margin-top: 1rem;
  font-style: italic;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.interactive-element {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #f59e0b;
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
}

.interactive-element h3 {
  color: #92400e;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.interactive-element h3::before {
  content: '🎮';
  font-size: 1.2rem;
}

.quiz-question {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-top: 1rem;
}

.quiz-options {
  margin: 1rem 0;
}

.quiz-options label {
  display: block;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.quiz-options label:hover {
  background: #e2e8f0;
}

.quiz-options input[type="radio"] {
  margin-left: 0.5rem;
}

.unit-navigation {
  background: var(--light-bg);
  padding: 2rem 0;
  margin-top: 3rem;
}

.nav-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-center {
  font-weight: 600;
  color: var(--text-secondary);
}

/* Glossary Specific Styles */
.glossary-controls {
  background: var(--light-bg);
  padding: 2rem 0;
}

.controls-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.search-section {
  margin-bottom: 2rem;
}

.search-box {
  position: relative;
  margin-bottom: 1rem;
}

.search-box i {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.search-box input {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.filter-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.alphabet-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.alphabet-label {
  font-weight: 600;
  color: var(--text-secondary);
}

.alphabet-buttons {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.alphabet-btn {
  width: 2rem;
  height: 2rem;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  font-weight: 600;
}

.alphabet-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.glossary-content {
  padding: 3rem 0;
}

.glossary-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 3rem;
  justify-content: center;
}

.stat-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 150px;
}

.stat-card i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.terms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.term-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.term-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
}

.term-header {
  margin-bottom: 1rem;
}

.term-arabic {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.term-english {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-style: italic;
}

.term-category-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.term-category-badge.technical {
  background: #dbeafe;
  color: #1e40af;
}

.term-category-badge.medical {
  background: #dcfce7;
  color: #166534;
}

.term-category-badge.management {
  background: #fef3c7;
  color: #92400e;
}

.term-category-badge.safety {
  background: #fee2e2;
  color: #991b1b;
}

.term-category-badge.quality {
  background: #f3e8ff;
  color: #7c3aed;
}

.term-preview {
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.term-actions {
  text-align: left;
}

.btn-link {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.btn-link:hover {
  color: var(--primary-dark);
}

.no-results {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-secondary);
}

.no-results i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-results h3 {
  margin-bottom: 1rem;
  color: var(--text-color);
}

/* Term Modal Styles */
.term-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.term-details > div {
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.term-details > div:last-child {
  border-bottom: none;
}

.related-terms {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.related-tag {
  background: var(--light-bg);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

/* Content Switch Styles */
.content-switch {
  margin: 2rem 0;
  display: flex;
  justify-content: center;
}

.switch-container {
  background: var(--gray-100);
  border-radius: 3rem;
  padding: 0.5rem;
  display: flex;
  gap: 0.5rem;
}

.switch-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 2.5rem;
  background: transparent;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
}

.switch-btn.active {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.switch-btn:hover:not(.active) {
  background: var(--gray-200);
  color: var(--text-color);
}

.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

/* Lectures Grid Styles */
.lectures-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
}

.lecture-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
  border: 1px solid var(--gray-200);
}

.lecture-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.lecture-card.coming-soon {
  opacity: 0.7;
  background: var(--gray-50);
}

.lecture-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.lecture-number {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.875rem;
}

.lecture-duration {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  opacity: 0.9;
}

.lecture-duration::before {
  content: '⏱️';
  font-size: 1rem;
}

.lecture-content {
  padding: var(--spacing-xl);
}

.lecture-content h3 {
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
  font-size: 1.25rem;
  line-height: 1.4;
}

.lecture-content p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.lecture-meta {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.lecture-meta span {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--gray-500);
  font-size: 0.875rem;
}

.lecture-topics {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.topic-tag {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.lecture-footer {
  padding: 0 var(--spacing-xl) var(--spacing-xl);
}

.lecture-btn {
  width: 100%;
  justify-content: center;
}

/* Lecture Modal Specific Styles */
.lecture-overview {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.lecture-stats {
  display: flex;
  gap: var(--spacing-xl);
  justify-content: center;
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.lecture-stats .stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.lecture-stats .stat i {
  color: var(--primary-color);
  font-size: 1.5rem;
}

.lecture-stats .stat span {
  font-weight: 600;
  color: var(--gray-700);
}

.lecture-description h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.lecture-outline h3 {
  color: var(--secondary-color);
  margin-bottom: var(--spacing-lg);
}

.outline-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.outline-item {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.outline-item:hover {
  background: var(--primary-color);
  color: white;
  transform: translateX(-5px);
}

.outline-number {
  background: var(--primary-color);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
  transition: background-color var(--transition-normal);
}

.outline-item:hover .outline-number {
  background: white;
  color: var(--primary-color);
}

.outline-content {
  flex: 1;
}

.outline-content h4 {
  margin-bottom: var(--spacing-sm);
  font-size: 1.125rem;
}

.outline-content p {
  margin-bottom: var(--spacing-sm);
  opacity: 0.8;
}

.outline-duration {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  opacity: 0.7;
}

.lecture-objectives h3 {
  color: var(--accent-color);
  margin-bottom: var(--spacing-lg);
}

.lecture-objectives ul {
  list-style: none;
  padding: 0;
}

.lecture-objectives li {
  position: relative;
  padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-xl);
  margin-bottom: var(--spacing-sm);
}

.lecture-objectives li::before {
  content: '✓';
  position: absolute;
  right: 0;
  top: var(--spacing-sm);
  color: var(--secondary-color);
  font-weight: bold;
}

.lecture-resources h3 {
  color: var(--accent-color);
  margin-bottom: var(--spacing-lg);
}

.resources-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.resource-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.resource-item:hover {
  background: var(--gray-100);
}

.resource-item i {
  color: var(--primary-color);
  font-size: 1.25rem;
  width: 20px;
  text-align: center;
}

.resource-item span {
  flex: 1;
  font-weight: 500;
}

.resource-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.resource-link:hover {
  background: var(--primary-color);
  color: white;
}

/* Responsive Design for Unit Content */
@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .content-sidebar {
    position: static;
    order: -1;
  }

  .unit-title {
    font-size: 2rem;
  }

  .unit-meta {
    gap: 1rem;
  }

  .nav-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .glossary-stats {
    flex-direction: column;
    align-items: center;
  }

  .terms-grid {
    grid-template-columns: 1fr;
  }

  .alphabet-buttons {
    justify-content: center;
  }

  .switch-container {
    flex-direction: column;
    width: 100%;
    max-width: 300px;
  }

  .switch-btn {
    justify-content: center;
  }

  .lectures-grid {
    grid-template-columns: 1fr;
  }

  .lecture-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .lecture-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .outline-item {
    flex-direction: column;
    text-align: center;
  }

  .outline-number {
    align-self: center;
  }

  .resources-list {
    gap: var(--spacing-sm);
  }

  .resource-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }
}
