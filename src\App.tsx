import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Navigation from './components/Navigation';
import HomePage from './pages/HomePage';
import Unit1Page from './pages/Unit1Page';
import Unit2Page from './pages/Unit2Page';
import Unit3Page from './pages/Unit3Page';
import Unit4Page from './pages/Unit4Page';
import Unit5Page from './pages/Unit5Page';
import GlossaryPage from './pages/GlossaryPage';
import Footer from './components/Footer';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <Header />
        <Navigation />
        
        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/unit1" element={<Unit1Page />} />
            <Route path="/unit2" element={<Unit2Page />} />
            <Route path="/unit3" element={<Unit3Page />} />
            <Route path="/unit4" element={<Unit4Page />} />
            <Route path="/unit5" element={<Unit5Page />} />
            <Route path="/glossary" element={<GlossaryPage />} />
          </Routes>
        </main>
        
        <Footer />
      </div>
    </Router>
  );
}

export default App;