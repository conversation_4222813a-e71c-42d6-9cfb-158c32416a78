import React from 'react';
import { BookOpen, Mail, Phone, GraduationCap } from 'lucide-react';

const Header: React.FC = () => {
  return (
    <header className="bg-gradient-to-l from-blue-900 via-blue-800 to-blue-900 text-white shadow-lg">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="flex justify-center items-center mb-4">
            <BookOpen className="h-12 w-12 text-blue-200 ml-3" />
            <h1 className="text-3xl md:text-4xl font-bold">
              منهج متكامل في الهندسة الإكلينيكية
            </h1>
          </div>
          
          <p className="text-xl text-blue-200 mb-4">
            من النظرية إلى التطبيق
          </p>
          
          <div className="bg-blue-800/50 rounded-lg p-6 max-w-4xl mx-auto">
            <div className="flex items-center justify-center mb-3">
              <GraduationCap className="h-6 w-6 text-blue-200 ml-2" />
              <h2 className="text-lg font-semibold">د. محمد يعقوب إسماعيل</h2>
            </div>
            
            <p className="text-blue-200 mb-4">
              جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية (SUST - BME)
            </p>
            
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <div className="flex items-center">
                <Mail className="h-4 w-4 ml-1" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center">
                <Phone className="h-4 w-4 ml-1" />
                <span>+966538076790</span>
              </div>
              <div className="flex items-center">
                <Phone className="h-4 w-4 ml-1" />
                <span>+249912867327</span>
              </div>
            </div>
            
            <div className="mt-4 text-sm text-blue-200">
              <p>سنة النشر: 2025 | الجمهور المستهدف: طلاب الهندسة الطبية الحيوية والمهندسون حديثو التخرج</p>
              <p className="mt-1">© 2025, Mohammed Yagoub Esmail. جميع الحقوق محفوظة</p>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;