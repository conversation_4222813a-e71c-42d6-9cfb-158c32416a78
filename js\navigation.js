/**
 * Navigation functionality for Clinical Engineering Curriculum
 * وظائف التنقل لمنهج الهندسة الإكلينيكية
 */

class NavigationManager {
    constructor() {
        this.navbar = document.querySelector('.navbar');
        this.navToggle = document.querySelector('.nav-toggle');
        this.navMenu = document.querySelector('.nav-menu');
        this.navLinks = document.querySelectorAll('.nav-link');
        this.sections = document.querySelectorAll('section[id]');
        
        this.isMenuOpen = false;
        this.currentSection = '';
        this.scrollThreshold = 50;
        
        this.init();
    }
    
    /**
     * Initialize navigation functionality
     */
    init() {
        this.bindEvents();
        this.updateActiveLink();
        this.setupKeyboardNavigation();
        this.setupMobileMenu();
        
        console.log('🧭 Navigation Manager initialized');
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // Scroll events
        window.addEventListener('scroll', this.throttle(this.handleScroll.bind(this), 16));
        
        // Mobile menu toggle
        if (this.navToggle) {
            this.navToggle.addEventListener('click', this.toggleMobileMenu.bind(this));
        }
        
        // Navigation links
        this.navLinks.forEach(link => {
            link.addEventListener('click', this.handleNavLinkClick.bind(this));
        });
        
        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // Outside click to close mobile menu
        document.addEventListener('click', this.handleOutsideClick.bind(this));
    }
    
    /**
     * Handle scroll events
     */
    handleScroll() {
        this.updateNavbarAppearance();
        this.updateActiveLink();
        this.updateProgressIndicator();
    }
    
    /**
     * Update navbar appearance on scroll
     */
    updateNavbarAppearance() {
        if (window.scrollY > this.scrollThreshold) {
            this.navbar.classList.add('scrolled');
        } else {
            this.navbar.classList.remove('scrolled');
        }
    }
    
    /**
     * Update active navigation link
     */
    updateActiveLink() {
        let currentSection = '';
        const scrollPosition = window.scrollY + 100;
        
        this.sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            
            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                currentSection = section.getAttribute('id');
            }
        });
        
        if (currentSection !== this.currentSection) {
            this.currentSection = currentSection;
            
            this.navLinks.forEach(link => {
                link.classList.remove('active');
                
                const href = link.getAttribute('href');
                if (href === `#${currentSection}`) {
                    link.classList.add('active');
                    this.announceCurrentSection(currentSection);
                }
            });
        }
    }
    
    /**
     * Update reading progress indicator
     */
    updateProgressIndicator() {
        const scrollTop = window.scrollY;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        // Update progress bar if it exists
        const progressBar = document.querySelector('.reading-progress');
        if (progressBar) {
            progressBar.style.width = `${scrollPercent}%`;
        }
        
        // Update progress in navbar
        this.navbar.style.setProperty('--scroll-progress', `${scrollPercent}%`);
    }
    
    /**
     * Handle navigation link clicks
     */
    handleNavLinkClick(event) {
        event.preventDefault();
        
        const link = event.currentTarget;
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
            this.scrollToSection(targetElement);
            
            // Close mobile menu if open
            if (this.isMenuOpen) {
                this.closeMobileMenu();
            }
            
            // Update URL without triggering scroll
            history.pushState(null, null, `#${targetId}`);
            
            // Announce navigation for screen readers
            if (window.announceToScreenReader) {
                window.announceToScreenReader(`انتقال إلى قسم ${targetElement.querySelector('h1, h2, h3')?.textContent || targetId}`);
            }
        }
    }
    
    /**
     * Smooth scroll to section
     */
    scrollToSection(element) {
        const offsetTop = element.offsetTop - (this.navbar.offsetHeight + 20);
        
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
    
    /**
     * Toggle mobile menu
     */
    toggleMobileMenu() {
        if (this.isMenuOpen) {
            this.closeMobileMenu();
        } else {
            this.openMobileMenu();
        }
    }
    
    /**
     * Open mobile menu
     */
    openMobileMenu() {
        this.isMenuOpen = true;
        this.navToggle.classList.add('active');
        this.navMenu.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Focus first menu item
        const firstLink = this.navMenu.querySelector('.nav-link');
        if (firstLink) {
            firstLink.focus();
        }
        
        // Announce menu opening
        if (window.announceToScreenReader) {
            window.announceToScreenReader('تم فتح قائمة التنقل');
        }
    }
    
    /**
     * Close mobile menu
     */
    closeMobileMenu() {
        this.isMenuOpen = false;
        this.navToggle.classList.remove('active');
        this.navMenu.classList.remove('active');
        document.body.style.overflow = '';
        
        // Return focus to toggle button
        this.navToggle.focus();
        
        // Announce menu closing
        if (window.announceToScreenReader) {
            window.announceToScreenReader('تم إغلاق قائمة التنقل');
        }
    }
    
    /**
     * Handle window resize
     */
    handleResize() {
        // Close mobile menu on desktop
        if (window.innerWidth >= 768 && this.isMenuOpen) {
            this.closeMobileMenu();
        }
    }
    
    /**
     * Handle clicks outside mobile menu
     */
    handleOutsideClick(event) {
        if (this.isMenuOpen && 
            !this.navMenu.contains(event.target) && 
            !this.navToggle.contains(event.target)) {
            this.closeMobileMenu();
        }
    }
    
    /**
     * Setup keyboard navigation
     */
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (event) => {
            switch (event.key) {
                case 'Escape':
                    if (this.isMenuOpen) {
                        this.closeMobileMenu();
                    }
                    break;
                    
                case 'Tab':
                    this.handleTabNavigation(event);
                    break;
                    
                case 'ArrowDown':
                case 'ArrowUp':
                    if (this.isMenuOpen) {
                        this.handleArrowNavigation(event);
                    }
                    break;
            }
        });
    }
    
    /**
     * Handle tab navigation in mobile menu
     */
    handleTabNavigation(event) {
        if (!this.isMenuOpen) return;
        
        const focusableElements = this.navMenu.querySelectorAll('.nav-link');
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (event.shiftKey) {
            // Shift + Tab
            if (document.activeElement === firstElement) {
                event.preventDefault();
                lastElement.focus();
            }
        } else {
            // Tab
            if (document.activeElement === lastElement) {
                event.preventDefault();
                firstElement.focus();
            }
        }
    }
    
    /**
     * Handle arrow key navigation in mobile menu
     */
    handleArrowNavigation(event) {
        event.preventDefault();
        
        const focusableElements = Array.from(this.navMenu.querySelectorAll('.nav-link'));
        const currentIndex = focusableElements.indexOf(document.activeElement);
        
        let nextIndex;
        if (event.key === 'ArrowDown') {
            nextIndex = (currentIndex + 1) % focusableElements.length;
        } else {
            nextIndex = (currentIndex - 1 + focusableElements.length) % focusableElements.length;
        }
        
        focusableElements[nextIndex].focus();
    }
    
    /**
     * Setup mobile menu enhancements
     */
    setupMobileMenu() {
        // Add swipe gestures for mobile menu
        let startY = 0;
        let startX = 0;
        
        this.navMenu.addEventListener('touchstart', (event) => {
            startY = event.touches[0].clientY;
            startX = event.touches[0].clientX;
        });
        
        this.navMenu.addEventListener('touchmove', (event) => {
            if (!this.isMenuOpen) return;
            
            const currentY = event.touches[0].clientY;
            const currentX = event.touches[0].clientX;
            const diffY = startY - currentY;
            const diffX = startX - currentX;
            
            // Close menu on swipe up
            if (diffY > 50 && Math.abs(diffX) < 100) {
                this.closeMobileMenu();
            }
        });
    }
    
    /**
     * Announce current section for accessibility
     */
    announceCurrentSection(sectionId) {
        const sectionNames = {
            'home': 'الصفحة الرئيسية',
            'about': 'حول المنهج',
            'curriculum': 'المحتوى',
            'units': 'الوحدات',
            'resources': 'المصادر',
            'contact': 'التواصل'
        };
        
        const sectionName = sectionNames[sectionId] || sectionId;
        
        // Debounce announcements
        clearTimeout(this.announceTimeout);
        this.announceTimeout = setTimeout(() => {
            if (window.announceToScreenReader) {
                window.announceToScreenReader(`القسم الحالي: ${sectionName}`);
            }
        }, 500);
    }
    
    /**
     * Throttle function for performance
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    /**
     * Get current active section
     */
    getCurrentSection() {
        return this.currentSection;
    }
    
    /**
     * Navigate to specific section programmatically
     */
    navigateToSection(sectionId) {
        const targetElement = document.getElementById(sectionId);
        if (targetElement) {
            this.scrollToSection(targetElement);
            history.pushState(null, null, `#${sectionId}`);
        }
    }
}

// Initialize navigation when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.navigationManager = new NavigationManager();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavigationManager;
}
