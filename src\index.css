@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Arabic text */
body {
  font-family: 'Noto Sans Arabic', system-ui, sans-serif;
  direction: rtl;
  text-align: right;
}

/* Scrollbar styles */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Arabic typography improvements */
.prose {
  direction: rtl;
  text-align: right;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  text-align: right;
  direction: rtl;
}

.prose p {
  text-align: right;
  direction: rtl;
  line-height: 1.8;
}

.prose ul,
.prose ol {
  direction: rtl;
  text-align: right;
}

/* Custom hover effects */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.6;
  }
  
  h1, h2, h3 {
    page-break-after: avoid;
  }
  
  .page-break {
    page-break-before: always;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Enhanced focus styles for accessibility */
*:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Custom gradient backgrounds */
.gradient-blue {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
}

.gradient-emerald {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
}

.gradient-amber {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
}

.gradient-rose {
  background: linear-gradient(135deg, #e11d48 0%, #f43f5e 100%);
}