/**
 * Animation controller for Clinical Engineering Curriculum
 * تحكم الرسوم المتحركة لمنهج الهندسة الإكلينيكية
 */

class AnimationController {
    constructor() {
        this.observers = new Map();
        this.animatedElements = new Set();
        this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        
        this.init();
    }
    
    /**
     * Initialize animation controller
     */
    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverAnimations();
        this.setupLoadingAnimations();
        this.setupCounterAnimations();
        this.setupParallaxEffects();
        
        console.log('🎬 Animation Controller initialized');
    }
    
    /**
     * Setup intersection observer for scroll animations
     */
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: [0.1, 0.3, 0.5],
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.prefersReducedMotion) {
                    this.triggerAnimation(entry.target, entry.intersectionRatio);
                }
            });
        }, observerOptions);
        
        this.observers.set('scroll', observer);
        
        // Observe elements with animation classes
        const animatedElements = document.querySelectorAll([
            '.scroll-animate',
            '.animate-on-scroll',
            '.fade-in-up',
            '.fade-in-left',
            '.fade-in-right',
            '.scale-in',
            '.slide-in-up'
        ].join(', '));
        
        animatedElements.forEach(element => {
            observer.observe(element);
        });
    }
    
    /**
     * Trigger animation for element
     */
    triggerAnimation(element, ratio) {
        if (this.animatedElements.has(element)) return;
        
        this.animatedElements.add(element);
        
        // Add base animation class
        element.classList.add('in-view');
        
        // Handle staggered animations for children
        const children = element.querySelectorAll('.animate-child');
        if (children.length > 0) {
            this.staggerChildAnimations(children);
        }
        
        // Handle specific animation types
        if (element.classList.contains('counter')) {
            this.animateCounter(element);
        }
        
        if (element.classList.contains('progress-bar')) {
            this.animateProgressBar(element);
        }
        
        if (element.classList.contains('typewriter')) {
            this.animateTypewriter(element);
        }
    }
    
    /**
     * Stagger animations for child elements
     */
    staggerChildAnimations(children) {
        children.forEach((child, index) => {
            setTimeout(() => {
                child.classList.add('animate-fadeInUp');
            }, index * 100);
        });
    }
    
    /**
     * Setup scroll-based animations
     */
    setupScrollAnimations() {
        // Parallax elements
        const parallaxElements = document.querySelectorAll('.parallax');
        
        if (parallaxElements.length > 0 && !this.prefersReducedMotion) {
            window.addEventListener('scroll', this.throttle(() => {
                this.updateParallax(parallaxElements);
            }, 16));
        }
        
        // Floating elements
        const floatingElements = document.querySelectorAll('.floating-card');
        floatingElements.forEach((element, index) => {
            if (!this.prefersReducedMotion) {
                element.style.animationDelay = `${index * 0.5}s`;
                element.classList.add('animate-float');
            }
        });
    }
    
    /**
     * Update parallax effect
     */
    updateParallax(elements) {
        const scrollTop = window.pageYOffset;
        
        elements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            const yPos = -(scrollTop * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }
    
    /**
     * Setup hover animations
     */
    setupHoverAnimations() {
        const hoverElements = document.querySelectorAll([
            '.hover-float',
            '.hover-scale',
            '.hover-rotate',
            '.hover-glow',
            '.hover-slide'
        ].join(', '));
        
        hoverElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                if (!this.prefersReducedMotion) {
                    element.classList.add('hovered');
                }
            });
            
            element.addEventListener('mouseleave', () => {
                element.classList.remove('hovered');
            });
        });
    }
    
    /**
     * Setup loading animations
     */
    setupLoadingAnimations() {
        // Skeleton loading
        const skeletonElements = document.querySelectorAll('.skeleton');
        skeletonElements.forEach(element => {
            setTimeout(() => {
                element.classList.remove('skeleton');
                element.classList.add('animate-fadeIn');
            }, Math.random() * 1000 + 500);
        });
        
        // Progressive image loading
        this.setupImageLoading();
    }
    
    /**
     * Setup progressive image loading
     */
    setupImageLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    this.loadImage(img);
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    /**
     * Load image with animation
     */
    loadImage(img) {
        const tempImg = new Image();
        tempImg.onload = () => {
            img.src = tempImg.src;
            img.classList.remove('loading');
            img.classList.add('animate-fadeIn');
        };
        tempImg.src = img.dataset.src;
    }
    
    /**
     * Setup counter animations
     */
    setupCounterAnimations() {
        const counters = document.querySelectorAll('.counter, .stat-number');
        
        counters.forEach(counter => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateCounter(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe(counter);
        });
    }
    
    /**
     * Animate counter numbers
     */
    animateCounter(element) {
        const target = parseInt(element.textContent.replace(/[^\d]/g, ''));
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = 0;
        
        const updateCounter = () => {
            current += increment;
            if (current < target) {
                element.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        };
        
        if (!this.prefersReducedMotion) {
            updateCounter();
        }
    }
    
    /**
     * Animate progress bars
     */
    animateProgressBar(element) {
        const progress = element.dataset.progress || 100;
        const bar = element.querySelector('.progress-fill') || element;
        
        if (!this.prefersReducedMotion) {
            setTimeout(() => {
                bar.style.width = `${progress}%`;
            }, 100);
        } else {
            bar.style.width = `${progress}%`;
        }
    }
    
    /**
     * Animate typewriter effect
     */
    animateTypewriter(element) {
        if (this.prefersReducedMotion) return;
        
        const text = element.textContent;
        const speed = element.dataset.speed || 50;
        
        element.textContent = '';
        element.style.borderRight = '2px solid';
        
        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, speed);
            } else {
                // Blinking cursor effect
                setInterval(() => {
                    element.style.borderRight = element.style.borderRight === '2px solid' 
                        ? '2px solid transparent' 
                        : '2px solid';
                }, 500);
            }
        };
        
        typeWriter();
    }
    
    /**
     * Setup parallax effects
     */
    setupParallaxEffects() {
        if (this.prefersReducedMotion) return;
        
        const parallaxElements = document.querySelectorAll('.parallax-bg');
        
        window.addEventListener('scroll', this.throttle(() => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            parallaxElements.forEach(element => {
                element.style.transform = `translateY(${rate}px)`;
            });
        }, 16));
    }
    
    /**
     * Create custom animation
     */
    createAnimation(element, keyframes, options = {}) {
        if (this.prefersReducedMotion) return;
        
        const defaultOptions = {
            duration: 1000,
            easing: 'ease-out',
            fill: 'forwards'
        };
        
        const animationOptions = { ...defaultOptions, ...options };
        
        return element.animate(keyframes, animationOptions);
    }
    
    /**
     * Animate element entrance
     */
    animateEntrance(element, type = 'fadeInUp') {
        if (this.prefersReducedMotion) {
            element.style.opacity = '1';
            return;
        }
        
        const animations = {
            fadeInUp: [
                { opacity: 0, transform: 'translateY(30px)' },
                { opacity: 1, transform: 'translateY(0)' }
            ],
            fadeInLeft: [
                { opacity: 0, transform: 'translateX(-30px)' },
                { opacity: 1, transform: 'translateX(0)' }
            ],
            fadeInRight: [
                { opacity: 0, transform: 'translateX(30px)' },
                { opacity: 1, transform: 'translateX(0)' }
            ],
            scaleIn: [
                { opacity: 0, transform: 'scale(0.8)' },
                { opacity: 1, transform: 'scale(1)' }
            ]
        };
        
        const keyframes = animations[type] || animations.fadeInUp;
        return this.createAnimation(element, keyframes);
    }
    
    /**
     * Animate element exit
     */
    animateExit(element, type = 'fadeOut') {
        if (this.prefersReducedMotion) {
            element.style.opacity = '0';
            return Promise.resolve();
        }
        
        const animations = {
            fadeOut: [
                { opacity: 1 },
                { opacity: 0 }
            ],
            slideUp: [
                { transform: 'translateY(0)' },
                { transform: 'translateY(-100%)' }
            ],
            scaleOut: [
                { transform: 'scale(1)' },
                { transform: 'scale(0)' }
            ]
        };
        
        const keyframes = animations[type] || animations.fadeOut;
        const animation = this.createAnimation(element, keyframes);
        
        return animation.finished;
    }
    
    /**
     * Throttle function for performance
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    /**
     * Cleanup animations
     */
    cleanup() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.animatedElements.clear();
    }
}

// Initialize animation controller when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.animationController = new AnimationController();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnimationController;
}
