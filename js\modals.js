/**
 * Modal functionality for Clinical Engineering Curriculum
 * وظائف النوافذ المنبثقة لمنهج الهندسة الإكلينيكية
 */

class ModalManager {
    constructor() {
        this.activeModal = null;
        this.init();
    }
    
    /**
     * Initialize modal functionality
     */
    init() {
        this.bindEvents();
        this.setupKeyboardNavigation();
        console.log('🪟 Modal Manager initialized');
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // Close modal when clicking outside
        document.addEventListener('click', (event) => {
            if (event.target.classList.contains('modal')) {
                this.closeModal(event.target.id);
            }
        });
        
        // Prevent modal content clicks from closing modal
        document.querySelectorAll('.modal-content').forEach(content => {
            content.addEventListener('click', (event) => {
                event.stopPropagation();
            });
        });
    }
    
    /**
     * Setup keyboard navigation
     */
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.activeModal) {
                this.closeModal(this.activeModal);
            }
        });
    }
    
    /**
     * Open modal
     */
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.error(`Modal with id "${modalId}" not found`);
            return;
        }
        
        // Close any existing modal
        if (this.activeModal) {
            this.closeModal(this.activeModal);
        }
        
        // Show modal
        modal.style.display = 'flex';
        modal.classList.add('modal-open');
        this.activeModal = modalId;
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        // Focus first focusable element
        const firstFocusable = modal.querySelector('button, input, textarea, select, a[href]');
        if (firstFocusable) {
            setTimeout(() => firstFocusable.focus(), 100);
        }
        
        // Announce to screen readers
        if (window.announceToScreenReader) {
            const title = modal.querySelector('.modal-header h2')?.textContent || 'نافذة منبثقة';
            window.announceToScreenReader(`تم فتح ${title}`);
        }
        
        // Add animation
        setTimeout(() => {
            modal.classList.add('modal-visible');
        }, 10);
    }
    
    /**
     * Close modal
     */
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;
        
        // Add closing animation
        modal.classList.remove('modal-visible');
        modal.classList.add('modal-closing');
        
        setTimeout(() => {
            modal.style.display = 'none';
            modal.classList.remove('modal-open', 'modal-closing');
            
            // Restore body scroll
            document.body.style.overflow = '';
            
            // Clear active modal
            if (this.activeModal === modalId) {
                this.activeModal = null;
            }
            
            // Announce to screen readers
            if (window.announceToScreenReader) {
                window.announceToScreenReader('تم إغلاق النافذة المنبثقة');
            }
        }, 300);
    }
    
    /**
     * Check if any modal is open
     */
    isModalOpen() {
        return this.activeModal !== null;
    }
    
    /**
     * Get active modal ID
     */
    getActiveModal() {
        return this.activeModal;
    }
}

// Initialize modal manager
let modalManager;
document.addEventListener('DOMContentLoaded', function() {
    modalManager = new ModalManager();
});

/**
 * Global functions for modal control
 */

/**
 * Open unit modal
 */
function openUnitModal(unitNumber) {
    const modalId = `unit${unitNumber}Modal`;
    if (modalManager) {
        modalManager.openModal(modalId);
    }
    
    // Track analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', 'unit_modal_open', {
            'unit_number': unitNumber,
            'event_category': 'engagement'
        });
    }
}

/**
 * Open glossary modal
 */
function openGlossaryModal() {
    if (modalManager) {
        modalManager.openModal('glossaryModal');
    }
    
    // Track analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', 'glossary_modal_open', {
            'event_category': 'engagement'
        });
    }
}

/**
 * Close modal
 */
function closeModal(modalId) {
    if (modalManager) {
        modalManager.closeModal(modalId);
    }
}

/**
 * Start unit study
 */
function startUnit(unitNumber) {
    // Close modal first
    closeModal(`unit${unitNumber}Modal`);
    
    // Show notification
    if (window.Utils) {
        window.Utils.showNotification(`بدء دراسة الوحدة ${unitNumber}`, 'success');
    }
    
    // Simulate navigation to unit content
    setTimeout(() => {
        // In a real application, this would navigate to the unit content
        console.log(`Starting Unit ${unitNumber}`);
        
        // For demo purposes, show a placeholder
        showUnitContent(unitNumber);
    }, 500);
    
    // Track analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', 'unit_start', {
            'unit_number': unitNumber,
            'event_category': 'learning'
        });
    }
}

/**
 * Show unit content (placeholder)
 */
function showUnitContent(unitNumber) {
    const content = `
        <div class="unit-content-placeholder">
            <h2>الوحدة ${unitNumber} - محتوى تعليمي</h2>
            <p>هنا سيتم عرض المحتوى التعليمي للوحدة ${unitNumber}</p>
            <div class="content-navigation">
                <button class="btn btn-secondary" onclick="backToHome()">
                    <i class="fas fa-home"></i>
                    العودة للرئيسية
                </button>
                <button class="btn btn-primary" onclick="nextChapter()">
                    <i class="fas fa-arrow-left"></i>
                    الفصل التالي
                </button>
            </div>
        </div>
    `;
    
    // Create overlay
    const overlay = document.createElement('div');
    overlay.className = 'content-overlay';
    overlay.innerHTML = content;
    document.body.appendChild(overlay);
    
    // Add styles
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
        color: white;
        text-align: center;
    `;
    
    // Auto close after 5 seconds
    setTimeout(() => {
        if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
        }
    }, 5000);
}

/**
 * Back to home function
 */
function backToHome() {
    // Remove any overlays
    const overlays = document.querySelectorAll('.content-overlay');
    overlays.forEach(overlay => {
        if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
        }
    });
    
    // Scroll to top
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
    
    // Show notification
    if (window.Utils) {
        window.Utils.showNotification('تم العودة للصفحة الرئيسية', 'info');
    }
}

/**
 * Next chapter function
 */
function nextChapter() {
    if (window.Utils) {
        window.Utils.showNotification('الانتقال للفصل التالي...', 'info');
    }
    
    // Simulate chapter navigation
    setTimeout(() => {
        backToHome();
    }, 1000);
}

/**
 * Search glossary
 */
function searchGlossary() {
    const searchInput = document.getElementById('glossarySearch');
    const searchTerm = searchInput.value.toLowerCase();
    const termItems = document.querySelectorAll('.term-item');
    
    termItems.forEach(item => {
        const title = item.querySelector('h4').textContent.toLowerCase();
        const description = item.querySelector('p').textContent.toLowerCase();
        
        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            item.style.display = 'block';
            // Highlight search term
            highlightSearchTerm(item, searchTerm);
        } else {
            item.style.display = 'none';
        }
    });
    
    // Show/hide sections based on visible terms
    const sections = document.querySelectorAll('.glossary-section');
    sections.forEach(section => {
        const visibleTerms = section.querySelectorAll('.term-item[style*="block"]');
        section.style.display = visibleTerms.length > 0 ? 'block' : 'none';
    });
}

/**
 * Highlight search term
 */
function highlightSearchTerm(element, term) {
    if (!term) return;
    
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    const textNodes = [];
    let node;
    
    while (node = walker.nextNode()) {
        textNodes.push(node);
    }
    
    textNodes.forEach(textNode => {
        const text = textNode.textContent;
        const regex = new RegExp(`(${term})`, 'gi');
        
        if (regex.test(text)) {
            const highlightedText = text.replace(regex, '<mark>$1</mark>');
            const span = document.createElement('span');
            span.innerHTML = highlightedText;
            textNode.parentNode.replaceChild(span, textNode);
        }
    });
}

/**
 * Download glossary
 */
function downloadGlossary() {
    // Create downloadable content
    const glossaryContent = generateGlossaryContent();
    const blob = new Blob([glossaryContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    // Create download link
    const link = document.createElement('a');
    link.href = url;
    link.download = 'مسرد-مصطلحات-الهندسة-الإكلينيكية.txt';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    URL.revokeObjectURL(url);
    
    // Show notification
    if (window.Utils) {
        window.Utils.showNotification('تم تحميل مسرد المصطلحات', 'success');
    }
    
    // Track analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', 'glossary_download', {
            'event_category': 'resource'
        });
    }
}

/**
 * Generate glossary content for download
 */
function generateGlossaryContent() {
    const terms = document.querySelectorAll('.term-item');
    let content = 'مسرد المصطلحات - منهج متكامل في الهندسة الإكلينيكية\n';
    content += '='.repeat(60) + '\n\n';
    
    terms.forEach(term => {
        const title = term.querySelector('h4').textContent;
        const description = term.querySelector('p').textContent;
        content += `${title}\n${'-'.repeat(title.length)}\n${description}\n\n`;
    });
    
    content += '\n© 2024 د. محمد يعقوب إسماعيل - جميع الحقوق محفوظة';
    
    return content;
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ModalManager,
        openUnitModal,
        openGlossaryModal,
        closeModal,
        startUnit,
        searchGlossary,
        downloadGlossary
    };
}
