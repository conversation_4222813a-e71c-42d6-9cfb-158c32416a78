import React from 'react';
import { Link } from 'react-router-dom';
import { BookOpen, Target, Users, Award, ArrowLeft } from 'lucide-react';

const HomePage: React.FC = () => {
  const units = [
    {
      number: 1,
      title: 'مدخل إلى عالم الهندسة الإكلينيكية',
      description: 'تعريف الهندسة الإكلينيكية ونشأتها التاريخية، أدوار ومسؤوليات المهندس الإكلينيكي',
      chapters: ['تعريف الهندسة الإكلينيكية ونشأتها', 'المهندس الإكلينيكي: الأدوار والمسؤوليات', 'بيئة الرعاية الصحية الحديثة'],
      path: '/unit1',
      color: 'blue'
    },
    {
      number: 2,
      title: 'ضمان الجودة وسلامة المرضى',
      description: 'دور الهندسة الإكلينيكية في تعزيز سلامة المرضى، اللوائح والمعايير الحاكمة',
      chapters: ['دور الهندسة الإكلينيكية في سلامة المرضى', 'اللوائح والمعايير وجهات الاعتماد'],
      path: '/unit2',
      color: 'emerald'
    },
    {
      number: 3,
      title: 'الإدارة المتكاملة للتكنولوجيا الطبية',
      description: 'إدارة دورة حياة الجهاز الطبي من التخطيط إلى الإخراج من الخدمة',
      chapters: ['التخطيط وتقييم التكنولوجيا', 'عملية الشراء والاستحواذ', 'إدارة الأصول والصيانة', 'التخطيط للاستبدال'],
      path: '/unit3',
      color: 'amber'
    },
    {
      number: 4,
      title: 'إدارة قسم الهندسة الإكلينيكية',
      description: 'بناء فريق فعال ونظام متكامل، إدارة الموارد والاستعانة بمصادر خارجية',
      chapters: ['هيكلة القسم النموذجي', 'إدارة الموارد البشرية والمالية', 'الاستعانة بمصادر خارجية وإدارة العقود'],
      path: '/unit4',
      color: 'purple'
    },
    {
      number: 5,
      title: 'آفاق عالمية وتوجهات مستقبلية',
      description: 'دراسات حالة عالمية، مستقبل الهندسة الإكلينيكية والتقنيات الناشئة',
      chapters: ['دراسات حالة عالمية', 'مستقبل الهندسة الإكلينيكية والذكاء الاصطناعي'],
      path: '/unit5',
      color: 'rose'
    }
  ];

  const objectives = [
    'فهم الأدوار الحيوية للمهندس الإكلينيكي في منظومة الرعاية الصحية',
    'إتقان مبادئ إدارة دورة حياة التكنولوجيا الطبية',
    'تطبيق معايير الجودة والسلامة في البيئة الإكلينيكية',
    'تطوير مهارات الإدارة والقيادة في بيئة الرعاية الصحية',
    'الاستعداد للتحديات المستقبلية والتقنيات الناشئة'
  ];

  return (
    <div className="max-w-7xl mx-auto">
      {/* المقدمة */}
      <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">مرحباً بكم في المنهج المتكامل</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            يقدم هذا المنهج دليلاً شاملاً للهندسة الإكلينيكية، من المفاهيم الأساسية إلى التطبيقات المتقدمة، 
            مصمم خصيصاً لطلاب الهندسة الطبية الحيوية والمهندسين حديثي التخرج.
          </p>
        </div>

        {/* أهداف المنهج */}
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          <div className="bg-blue-50 rounded-xl p-6">
            <div className="flex items-center mb-4">
              <Target className="h-6 w-6 text-blue-600 ml-2" />
              <h3 className="text-xl font-semibold text-blue-900">أهداف المنهج</h3>
            </div>
            <ul className="space-y-3">
              {objectives.map((objective, index) => (
                <li key={index} className="flex items-start">
                  <div className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold ml-3 mt-0.5 flex-shrink-0">
                    {index + 1}
                  </div>
                  <span className="text-blue-800">{objective}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-emerald-50 rounded-xl p-6">
            <div className="flex items-center mb-4">
              <Users className="h-6 w-6 text-emerald-600 ml-2" />
              <h3 className="text-xl font-semibold text-emerald-900">الجمهور المستهدف</h3>
            </div>
            <div className="space-y-4">
              <div className="border-r-4 border-emerald-500 pr-4">
                <h4 className="font-semibold text-emerald-800">طلاب الهندسة الطبية الحيوية</h4>
                <p className="text-emerald-700 text-sm">المقررات الأساسية والمتقدمة</p>
              </div>
              <div className="border-r-4 border-emerald-500 pr-4">
                <h4 className="font-semibold text-emerald-800">المهندسون حديثو التخرج</h4>
                <p className="text-emerald-700 text-sm">التأهيل المهني والتطوير المستمر</p>
              </div>
              <div className="border-r-4 border-emerald-500 pr-4">
                <h4 className="font-semibold text-emerald-800">المتخصصون في الرعاية الصحية</h4>
                <p className="text-emerald-700 text-sm">فهم التكنولوجيا الطبية</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* الوحدات */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">محتويات المنهج</h2>
        
        {units.map((unit) => (
          <div key={unit.number} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <div className={`bg-${unit.color}-600 text-white rounded-lg px-3 py-1 text-sm font-semibold ml-3`}>
                      الوحدة {unit.number}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">{unit.title}</h3>
                  </div>
                  <p className="text-gray-600 mb-4">{unit.description}</p>
                  
                  <div className="grid sm:grid-cols-2 gap-2">
                    {unit.chapters.map((chapter, index) => (
                      <div key={index} className="flex items-center text-sm text-gray-700">
                        <BookOpen className="h-4 w-4 text-gray-400 ml-2 flex-shrink-0" />
                        <span>الفصل {index + 1}: {chapter}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Link
                  to={unit.path}
                  className={`bg-${unit.color}-600 hover:bg-${unit.color}-700 text-white px-6 py-2 rounded-lg transition-colors flex items-center font-medium`}
                >
                  <span>دراسة الوحدة</span>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* مسرد المصطلحات */}
      <div className="mt-8 bg-gradient-to-l from-gray-50 to-blue-50 rounded-xl p-6">
        <div className="text-center">
          <h3 className="text-xl font-bold text-gray-900 mb-4">مسرد المصطلحات الشامل</h3>
          <p className="text-gray-600 mb-4">
            مرجع سريع للمصطلحات والمختصرات الرئيسية في الهندسة الإكلينيكية
          </p>
          <Link
            to="/glossary"
            className="inline-flex items-center bg-gray-700 hover:bg-gray-800 text-white px-6 py-3 rounded-lg transition-colors font-medium"
          >
            <span>استعراض المسرد</span>
            <ArrowLeft className="h-4 w-4 mr-2" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HomePage;