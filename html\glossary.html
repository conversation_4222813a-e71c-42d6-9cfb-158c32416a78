<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مسرد المصطلحات - منهج متكامل في الهندسة الإكلينيكية</title>
    <meta name="description" content="مرجع شامل للمصطلحات والمختصرات الرئيسية في الهندسة الإكلينيكية">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="../assets/icons/logo.svg" alt="شعار المنهج" class="logo-img">
                <span class="logo-text">منهج الهندسة الإكلينيكية</span>
            </div>
            <div class="nav-menu">
                <a href="../index.html" class="nav-link">الرئيسية</a>
                <a href="about.html" class="nav-link">حول المنهج</a>
                <a href="curriculum.html" class="nav-link">المحتوى</a>
                <a href="units.html" class="nav-link">الوحدات</a>
                <a href="resources.html" class="nav-link">المصادر</a>
                <a href="contact.html" class="nav-link">التواصل</a>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="header-content">
                <h1 class="page-title">مسرد المصطلحات</h1>
                <p class="page-subtitle">مرجع شامل للمصطلحات والمختصرات الرئيسية في الهندسة الإكلينيكية</p>
                <nav class="breadcrumb">
                    <a href="../index.html">الرئيسية</a>
                    <span class="separator">/</span>
                    <span class="current">مسرد المصطلحات</span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Search and Filter -->
    <section class="glossary-controls">
        <div class="container">
            <div class="controls-card">
                <div class="search-section">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="glossary-search" placeholder="ابحث عن مصطلح...">
                    </div>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">الكل</button>
                        <button class="filter-btn" data-filter="technical">تقني</button>
                        <button class="filter-btn" data-filter="medical">طبي</button>
                        <button class="filter-btn" data-filter="management">إداري</button>
                        <button class="filter-btn" data-filter="safety">سلامة</button>
                        <button class="filter-btn" data-filter="quality">جودة</button>
                    </div>
                </div>
                <div class="alphabet-nav">
                    <span class="alphabet-label">التصفح الأبجدي:</span>
                    <div class="alphabet-buttons">
                        <button class="alphabet-btn" data-letter="أ">أ</button>
                        <button class="alphabet-btn" data-letter="ب">ب</button>
                        <button class="alphabet-btn" data-letter="ت">ت</button>
                        <button class="alphabet-btn" data-letter="ث">ث</button>
                        <button class="alphabet-btn" data-letter="ج">ج</button>
                        <button class="alphabet-btn" data-letter="ح">ح</button>
                        <button class="alphabet-btn" data-letter="خ">خ</button>
                        <button class="alphabet-btn" data-letter="د">د</button>
                        <button class="alphabet-btn" data-letter="ذ">ذ</button>
                        <button class="alphabet-btn" data-letter="ر">ر</button>
                        <button class="alphabet-btn" data-letter="ز">ز</button>
                        <button class="alphabet-btn" data-letter="س">س</button>
                        <button class="alphabet-btn" data-letter="ش">ش</button>
                        <button class="alphabet-btn" data-letter="ص">ص</button>
                        <button class="alphabet-btn" data-letter="ض">ض</button>
                        <button class="alphabet-btn" data-letter="ط">ط</button>
                        <button class="alphabet-btn" data-letter="ظ">ظ</button>
                        <button class="alphabet-btn" data-letter="ع">ع</button>
                        <button class="alphabet-btn" data-letter="غ">غ</button>
                        <button class="alphabet-btn" data-letter="ف">ف</button>
                        <button class="alphabet-btn" data-letter="ق">ق</button>
                        <button class="alphabet-btn" data-letter="ك">ك</button>
                        <button class="alphabet-btn" data-letter="ل">ل</button>
                        <button class="alphabet-btn" data-letter="م">م</button>
                        <button class="alphabet-btn" data-letter="ن">ن</button>
                        <button class="alphabet-btn" data-letter="ه">ه</button>
                        <button class="alphabet-btn" data-letter="و">و</button>
                        <button class="alphabet-btn" data-letter="ي">ي</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Glossary Content -->
    <section class="glossary-content">
        <div class="container">
            <div class="glossary-layout">
                <!-- Statistics -->
                <div class="glossary-stats">
                    <div class="stat-card">
                        <i class="fas fa-book"></i>
                        <div class="stat-info">
                            <span class="stat-number" id="total-terms">120</span>
                            <span class="stat-label">مصطلح</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-language"></i>
                        <div class="stat-info">
                            <span class="stat-number">2</span>
                            <span class="stat-label">لغة</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-tags"></i>
                        <div class="stat-info">
                            <span class="stat-number">6</span>
                            <span class="stat-label">فئة</span>
                        </div>
                    </div>
                </div>

                <!-- Terms List -->
                <div class="terms-container">
                    <div class="terms-grid" id="terms-grid">
                        <!-- Terms will be populated by JavaScript -->
                    </div>
                    
                    <div class="no-results" id="no-results" style="display: none;">
                        <i class="fas fa-search"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على مصطلحات تطابق البحث. جرب كلمات مختلفة.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Term Modal -->
    <div class="modal" id="term-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-term-title"></h3>
                <button class="modal-close" id="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="term-details">
                    <div class="term-english" id="modal-term-english"></div>
                    <div class="term-category" id="modal-term-category"></div>
                    <div class="term-definition" id="modal-term-definition"></div>
                    <div class="term-examples" id="modal-term-examples"></div>
                    <div class="term-related" id="modal-term-related"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/navigation.js"></script>
    <script src="../js/animations.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        // Glossary data
        const glossaryTerms = [
            {
                id: 1,
                arabic: "الهندسة الإكلينيكية",
                english: "Clinical Engineering",
                category: "technical",
                definition: "تخصص هندسي متعدد التخصصات يجمع بين المبادئ الهندسية والعلوم الطبية لتطوير وإدارة وصيانة التكنولوجيا الطبية المستخدمة في الرعاية الصحية.",
                examples: ["إدارة الأجهزة الطبية", "ضمان سلامة المرضى", "تدريب الكوادر الطبية"],
                related: ["المهندس الإكلينيكي", "التكنولوجيا الطبية", "إدارة المخاطر"]
            },
            {
                id: 2,
                arabic: "المهندس الإكلينيكي",
                english: "Clinical Engineer",
                category: "technical",
                definition: "مهندس متخصص في تطبيق المبادئ الهندسية في البيئة الطبية، مسؤول عن إدارة التكنولوجيا الطبية وضمان سلامة المرضى.",
                examples: ["تقييم الأجهزة الطبية", "إدارة برامج الصيانة", "التحقيق في الحوادث"],
                related: ["الهندسة الإكلينيكية", "إدارة التكنولوجيا", "سلامة المرضى"]
            },
            {
                id: 3,
                arabic: "سلامة المرضى",
                english: "Patient Safety",
                category: "safety",
                definition: "تجنب الأضرار التي قد تلحق بالمرضى نتيجة الرعاية الصحية المقدمة لهم، وتشمل منع الأخطاء الطبية والحد من المخاطر.",
                examples: ["منع الأخطاء الطبية", "تقليل مخاطر العدوى", "ضمان سلامة الأجهزة"],
                related: ["إدارة المخاطر", "ضمان الجودة", "الأحداث الضائرة"]
            },
            {
                id: 4,
                arabic: "ضمان الجودة",
                english: "Quality Assurance",
                category: "quality",
                definition: "مجموعة من الأنشطة المخططة والمنهجية المطبقة في نظام الجودة لضمان أن متطلبات الجودة للمنتج أو الخدمة سيتم الوفاء بها.",
                examples: ["مراجعة العمليات", "تدقيق الأنظمة", "تطبيق المعايير"],
                related: ["مراقبة الجودة", "التحسين المستمر", "ISO 9001"]
            },
            {
                id: 5,
                arabic: "إدارة المخاطر",
                english: "Risk Management",
                category: "safety",
                definition: "عملية منهجية لتحديد وتقييم والتحكم في المخاطر المرتبطة بالأجهزة والأنظمة الطبية.",
                examples: ["تحليل المخاطر", "تقييم الاحتمالية", "وضع خطط التخفيف"],
                related: ["تحليل الأسباب الجذرية", "FMEA", "مصفوفة المخاطر"]
            },
            {
                id: 6,
                arabic: "الصيانة الوقائية",
                english: "Preventive Maintenance",
                category: "technical",
                definition: "صيانة منتظمة ومجدولة للأجهزة الطبية لمنع الأعطال وضمان الأداء الأمثل.",
                examples: ["الفحص الدوري", "استبدال القطع", "المعايرة"],
                related: ["الصيانة التصحيحية", "إدارة الأصول", "دورة حياة الجهاز"]
            },
            {
                id: 7,
                arabic: "التكنولوجيا الطبية",
                english: "Medical Technology",
                category: "technical",
                definition: "جميع الأجهزة والمعدات والأنظمة المستخدمة في تشخيص وعلاج ومراقبة المرضى.",
                examples: ["أجهزة التصوير", "أجهزة المراقبة", "أجهزة العلاج"],
                related: ["الأجهزة الطبية", "التقنيات الحديثة", "الابتكار الطبي"]
            },
            {
                id: 8,
                arabic: "تحليل الأسباب الجذرية",
                english: "Root Cause Analysis (RCA)",
                category: "quality",
                definition: "منهجية منظمة لتحديد الأسباب الأساسية للمشاكل أو الحوادث لمنع تكرارها.",
                examples: ["تحليل الحوادث", "خريطة السبب والأثر", "الأسئلة الخمسة"],
                related: ["إدارة المخاطر", "التحسين المستمر", "الأحداث الضائرة"]
            },
            {
                id: 9,
                arabic: "المعايرة",
                english: "Calibration",
                category: "technical",
                definition: "عملية مقارنة قراءات الجهاز مع معايير معروفة لضمان دقة القياسات.",
                examples: ["معايرة أجهزة الضغط", "معايرة أجهزة الحرارة", "معايرة أجهزة التحليل"],
                related: ["دقة القياس", "المعايير المرجعية", "شهادات المعايرة"]
            },
            {
                id: 10,
                arabic: "إدارة الأصول",
                english: "Asset Management",
                category: "management",
                definition: "إدارة شاملة لدورة حياة الأجهزة الطبية من الشراء إلى الإخراج من الخدمة.",
                examples: ["تخطيط الشراء", "إدارة المخزون", "تتبع الأصول"],
                related: ["دورة حياة الجهاز", "إدارة التكاليف", "التخطيط الاستراتيجي"]
            }
        ];

        // Initialize glossary
        document.addEventListener('DOMContentLoaded', function() {
            renderTerms(glossaryTerms);
            setupEventListeners();
            updateStats();
        });

        function renderTerms(terms) {
            const termsGrid = document.getElementById('terms-grid');
            const noResults = document.getElementById('no-results');
            
            if (terms.length === 0) {
                termsGrid.style.display = 'none';
                noResults.style.display = 'block';
                return;
            }
            
            termsGrid.style.display = 'grid';
            noResults.style.display = 'none';
            
            termsGrid.innerHTML = terms.map(term => `
                <div class="term-card" data-category="${term.category}" data-term="${term.arabic}" onclick="openTermModal(${term.id})">
                    <div class="term-header">
                        <h3 class="term-arabic">${term.arabic}</h3>
                        <span class="term-english">${term.english}</span>
                    </div>
                    <div class="term-category-badge ${term.category}">${getCategoryName(term.category)}</div>
                    <p class="term-preview">${term.definition.substring(0, 100)}...</p>
                    <div class="term-actions">
                        <button class="btn-link">عرض التفاصيل <i class="fas fa-arrow-left"></i></button>
                    </div>
                </div>
            `).join('');
        }

        function getCategoryName(category) {
            const categories = {
                'technical': 'تقني',
                'medical': 'طبي',
                'management': 'إداري',
                'safety': 'سلامة',
                'quality': 'جودة'
            };
            return categories[category] || category;
        }

        function setupEventListeners() {
            // Search functionality
            const searchInput = document.getElementById('glossary-search');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const filteredTerms = glossaryTerms.filter(term => 
                    term.arabic.toLowerCase().includes(searchTerm) ||
                    term.english.toLowerCase().includes(searchTerm) ||
                    term.definition.toLowerCase().includes(searchTerm)
                );
                renderTerms(filteredTerms);
            });

            // Filter buttons
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    const filter = this.dataset.filter;
                    const filteredTerms = filter === 'all' ? 
                        glossaryTerms : 
                        glossaryTerms.filter(term => term.category === filter);
                    
                    renderTerms(filteredTerms);
                });
            });

            // Alphabet navigation
            const alphabetButtons = document.querySelectorAll('.alphabet-btn');
            alphabetButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const letter = this.dataset.letter;
                    const filteredTerms = glossaryTerms.filter(term => 
                        term.arabic.startsWith(letter)
                    );
                    renderTerms(filteredTerms);
                });
            });

            // Modal close
            const modalClose = document.getElementById('modal-close');
            const modal = document.getElementById('term-modal');
            
            modalClose.addEventListener('click', function() {
                modal.style.display = 'none';
            });
            
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        function openTermModal(termId) {
            const term = glossaryTerms.find(t => t.id === termId);
            if (!term) return;
            
            document.getElementById('modal-term-title').textContent = term.arabic;
            document.getElementById('modal-term-english').innerHTML = `<strong>English:</strong> ${term.english}`;
            document.getElementById('modal-term-category').innerHTML = `<strong>الفئة:</strong> ${getCategoryName(term.category)}`;
            document.getElementById('modal-term-definition').innerHTML = `<strong>التعريف:</strong> ${term.definition}`;
            
            if (term.examples && term.examples.length > 0) {
                document.getElementById('modal-term-examples').innerHTML = `
                    <strong>أمثلة:</strong>
                    <ul>${term.examples.map(example => `<li>${example}</li>`).join('')}</ul>
                `;
            }
            
            if (term.related && term.related.length > 0) {
                document.getElementById('modal-term-related').innerHTML = `
                    <strong>مصطلحات ذات صلة:</strong>
                    <div class="related-terms">${term.related.map(related => `<span class="related-tag">${related}</span>`).join('')}</div>
                `;
            }
            
            document.getElementById('term-modal').style.display = 'flex';
        }

        function updateStats() {
            document.getElementById('total-terms').textContent = glossaryTerms.length;
        }
    </script>
</body>
</html>
